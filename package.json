{"name": "nest-grapql", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migrate:generate": "ts-node -r tsconfig-paths/register migrate-generate.js", "migrate:create": "ts-node migrate-create.js", "migrate:up": "ts-node ./node_modules/typeorm/cli.js migration:run -d src/configs/data-source.conf.ts", "migrate:down": "ts-node ./node_modules/typeorm/cli.js migration:revert -d src/configs/data-source.conf.ts", "migrate:down:all": "node migrate-rollback-all.js"}, "dependencies": {"@apollo/server": "^4.11.3", "@aws-sdk/client-lightsail": "^3.848.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/apollo": "^13.0.3", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.1", "@nestjs/graphql": "^13.0.3", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "@nestjs/typeorm": "^11.0.0", "@types/adm-zip": "^0.5.7", "@types/compression": "^1.7.5", "@types/connect-timeout": "^1.9.0", "@types/lodash": "^4.17.16", "@types/multer": "^1.4.12", "@types/nodemailer": "^6.4.17", "@types/xml2js": "^0.4.14", "adm-zip": "^0.5.16", "apollo-server-express": "^3.13.0", "aws-sdk": "^2.1692.0", "axios": "^1.10.0", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.8.0", "connect-timeout": "^1.9.0", "csv-parser": "^3.2.0", "dataloader": "^2.2.3", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "graphql": "^16.10.0", "graphql-depth-limit": "^1.1.0", "graphql-tools": "^9.0.13", "graphql-type-json": "^0.3.2", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mongoose": "^8.16.4", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.13.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "slugify": "^1.6.6", "stripe": "^18.0.0", "typeorm": "^0.3.20", "xml2js": "^0.6.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/graphql-depth-limit": "^1.1.6", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.14.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}