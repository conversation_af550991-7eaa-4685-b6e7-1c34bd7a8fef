NODE_ENV=development

#Database
PG_HOST=localhost
PG_PORT=5432
PG_USER=postgres
PG_PASS=postgres
PG_DB=test


#App
CORS_ORIGIN=*
APP_PORT=8003
PORT=3000
JWT_SECRET=your-jwt-secret-key
AT_SECRET=
AT_TIMEOUT=24h
RT_SECRET=
RT_TIMEOUT=30d
LIMIT_SEND_FORGOT_PASS=5
OTP_EXPIRATION_TIME=1#hours
PAGE_DEFAULT=20
LIMIT_UPLOAD_SIZE=5#MB
REQUEST_TIMEOUT=15#minutes
API_URL=http://localhost:8003/
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR_SLACK_WEBHOOK_URL
ROOT_PATH=

#NameCheap
NC_API_URL=https://api.namecheap.com/xml.response
NC_API_USER=
NC_API_KEY=
NC_CLIENT_IP=

#Stripe
STRIPE_SECRET_KEY=sk_test_VePHdqKTYQjKNInc7u56JBrQ

#Mail
MAIL_HOST=
MAIL_PORT=
MAIL_USER=
MAIL_PASSWORD=
MAIL_FROM=
SUPPORT_MAIL=

#Weaveform
DEVW_API_URL=https://dev-weaveform.ip-tribe.com/
DESW_API_URL=https://designer-weaveform.ip-tribe.com/
W_API_KEY=

#Reviewsieuxe
RS_API_URL=https://ipt-wp-api.reviewsieuxe.com/

#AI Service
AI_API_URL=https://openrouter.ai/api
AI_API_KEY=your-openrouter-api-key

#Wordpress
WP_ADMIN_PASS=
WP_FILE_URL_DEFAULT=

#AWS
AWS_REGION=ap-southeast-1
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
SSH_KEY_BUCKET=your-ssh-key-bucket

#Cloudflare
CF_API_TOKEN=your-cloudflare-api-token
CF_ZONE_ID=your-cloudflare-zone-id

#Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0
