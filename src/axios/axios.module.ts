import { Global, Module } from '@nestjs/common';
import { NamecheapAxios } from './namecheap.axios';
import { WeaveformAxios } from './weaveform.axios';
import { ReviewsieuxeAxios } from './reviewsieuxe.axios';
import { CloudflareAxios } from './cloudflare.axios';
import { AIAxios } from './ai.axios';
import { AIResolver } from './ai.resolver';
import { WordPressLightsailModule } from '../modules/wordpress-lightsail/wordpress-lightsail.module';

@Global()
@Module({
    imports: [WordPressLightsailModule],
    providers: [NamecheapAxios, WeaveformAxios, ReviewsieuxeAxios, CloudflareAxios, AIAxios, AIResolver],
    exports: [NamecheapAxios, WeaveformAxios, ReviewsieuxeAxios, CloudflareAxios, AIAxios],
})
export class AxiosModule {}
