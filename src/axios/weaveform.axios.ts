import { BadRequestException, Injectable } from '@nestjs/common';
import axios from 'axios';
import appConf from '../configs/app.conf';
import { UserRoles } from '../entities/user.entity';

export interface ICreateUserWeaveform {
    email: string;
    first_name: string;
    last_name: string;
    username?: string;
    phone?: string;
    password: string;
    role_id: number;
}

export interface IUpdateUserWeaveform {
    email: string;
    first_name?: string;
    last_name?: string;
    username?: string;
    phone?: string;
    password?: string;
    role_id?: number;
}

@Injectable()
export class WeaveformAxios {
    private readonly DEV_API_URL = appConf.DEVW_API_URL;
    private readonly DES_API_URL = appConf.DESW_API_URL;
    private readonly API_KEY = appConf.W_API_KEY;

    private getApiUrl(role_id?: number) {
        switch (role_id) {
            case UserRoles.CUSTOMER:
                return this.DEV_API_URL;
            case UserRoles.DESIGNER:
                return this.DES_API_URL;
            default:
                return null;
        }
    }

    private getHeaders() {
        return {
            Authorization: `Bearer ${this.API_KEY}`,
            'Content-Type': 'application/json',
        };
    }

    async createUser(body: ICreateUserWeaveform) {
        const url = this.getApiUrl(body.role_id);
        if (url) {
            return axios
                .post(`${url}wp-json/ipt-sync-user/v1/users/sync`, body, { headers: this.getHeaders() })
                .then((response) => {
                    if (!response.data.success) {
                        throw new Error(response.data.message || 'Unknown error');
                    }
                    return response.data;
                })
                .catch((error) => {
                    throw new Error(error.response?.data?.message || error.message || 'Unknown error');
                });
        }
    }

    async updateUser(body: IUpdateUserWeaveform) {
        const url = this.getApiUrl(body.role_id);
        return axios
            .put(`${url}/wp-json/ipt-sync-user/v1/users/update`, body, { headers: this.getHeaders() })
            .then((response) => {
                if (!response.data.success) {
                    throw new Error(response.data.message || 'Unknown error');
                }
                return response.data;
            })
            .catch((error) => {
                throw new Error(error.response?.data?.message || error.message || 'Unknown error');
            });
    }
}
