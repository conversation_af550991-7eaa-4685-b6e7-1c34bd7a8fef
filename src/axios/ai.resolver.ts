import { Args, Mutation, Resolver } from '@nestjs/graphql';
import { AIAxios } from './ai.axios';
import { ChatCompletionInput } from './dtos/chat-completion-input.dto';
import { ContentGenerationInput } from './dtos/content-generation-input.dto';
import { AIResponseModel } from './models/ai-response.model';
import { UseGuards } from '@nestjs/common';
import { WebhookTokenGuard } from '../commons/guards/webhook-token.guard';

@UseGuards(WebhookTokenGuard)
export class AIResolver {
    constructor(private readonly aiAxios: AIAxios) {}

    @Mutation(() => AIResponseModel, { name: 'chat_completion' })
    async chatCompletion(@Args('input') input: ChatCompletionInput): Promise<AIResponseModel> {
        console.log('GraphQL - Received chat completion input:', JSON.stringify(input, null, 2));

        // Validation is handled by class-validator decorators in the InputType
        const { messages, model = 'llama-3.2-3b', temperature = 0.4, top_p = 1, max_tokens = 800 } = input;

        // Convert GraphQL input to the format expected by the axios service
        const messagesForAxios = messages.map((msg) => ({
            role: msg.role,
            content: msg.content,
        }));

        console.log('GraphQL - Calling AI service with messages:', messagesForAxios);

        try {
            const result = await this.aiAxios.chatCompletion(messagesForAxios, model, temperature, top_p, max_tokens);

            return {
                success: result.success,
                data: result.data,
                message: result.message,
            };
        } catch (error) {
            console.error('GraphQL - AI service error:', error);
            throw error;
        }
    }

    @Mutation(() => AIResponseModel, { name: 'generate_content' })
    async generateContent(@Args('input') input: ContentGenerationInput): Promise<AIResponseModel> {
        console.log('GraphQL - Received content generation input:', JSON.stringify(input, null, 2));

        const { faqs, inputFields } = input;

        console.log('GraphQL - Calling AI content generation service');

        try {
            const result = await this.aiAxios.generateContentCompletion(faqs, inputFields);

            // Extract the AI response content and parse it into structured array
            if (result.success && result.data?.choices?.[0]?.message?.content) {
                const aiResponseContent = result.data.choices[0].message.content;
                console.log('Raw AI Response:', aiResponseContent);

                // Parse the response into structured array
                const parsedContent = this.aiAxios.parseContentGenerationResponse(aiResponseContent);
                console.log('Parsed Content Array:', JSON.stringify(parsedContent, null, 2));

                return {
                    success: result.success,
                    data: {
                        ...result.data,
                        parsedContent: parsedContent,
                    },
                    message: result.message,
                };
            }

            return {
                success: result.success,
                data: result.data,
                message: result.message,
            };
        } catch (error) {
            console.error('GraphQL - AI content generation error:', error);
            throw error;
        }
    }
}
