import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import axios from 'axios';
import { parseStringPromise } from 'xml2js';
import appConf from '../configs/app.conf';

@Injectable()
export class NamecheapAxios {
    private readonly API_URL = appConf.NC_API_URL; // URL API Sandbox
    private readonly API_USER = appConf.NC_API_USER; // Tên người dùng API
    private readonly API_KEY = appConf.NC_API_KEY; // Khóa API
    private readonly CLIENT_IP = appConf.NC_CLIENT_IP; // Địa chỉ IP của bạn

    async exect(queryString: string) {
        const params = new URLSearchParams({
            ApiUser: this.API_USER,
            ApiKey: this.API_KEY,
            UserName: this.API_USER,
            ClientIp: this.CLIENT_IP,
        });

        try {
            console.log(`Calling Namecheap API with query: ${this.API_URL}?${params.toString()}&${queryString}`);
            const response = await axios.get(`${this.API_URL}?${params.toString()}&${queryString}`);
            return parseStringPromise(response.data, { explicitArray: false });
        } catch (error) {
            throw new HttpException(error.response?.data || 'Error calling Namecheap API', HttpStatus.BAD_REQUEST);
        }
    }
}
