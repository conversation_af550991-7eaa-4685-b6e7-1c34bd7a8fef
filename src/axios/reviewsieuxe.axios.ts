import { Injectable, InternalServerErrorException } from '@nestjs/common';
import axios from 'axios';
import appConf from '../configs/app.conf';

export interface ILoginResponse {
    token: string;
}

@Injectable()
export class ReviewsieuxeAxios {
    private readonly API_URL = appConf.RS_API_URL;
    private readonly BUNDLE_ID = appConf.RS_BUNDLE_ID;
    private readonly USERNAME = appConf.RS_USERNAME;
    private readonly PASSWORD = appConf.RS_PASSWORD;

    async login(): Promise<ILoginResponse> {
        return axios
            .post(
                `${this.API_URL}login`,
                {
                    username: this.USERNAME,
                    password: this.PASSWORD,
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            )
            .then((response) => {
                return response.data;
            })
            .catch((error) => {
                throw new InternalServerErrorException(
                    error.response?.data?.message || error.message || 'Unknown error'
                );
            });
    }

    //không cần chờ kết quả chỉ cần gửi đi
    async deploy(domain: string, plugins: string[]) {
        const token = await this.login();
        if (!token.token) {
            throw new InternalServerErrorException('Login failed');
        }
        // Fire-and-forget
        axios
            .post(
                `${this.API_URL}deploy`,
                {
                    domain_name: domain,
                    bundle_id: this.BUNDLE_ID,
                    plugins,
                },
                {
                    headers: {
                        Authorization: `Bearer ${token.token}`,
                        'Content-Type': 'application/json',
                    },
                }
            )
            .catch((e) => {
                console.log(e);
            });
    }

    //Chờ kết quả
    async deployAsync(domain: string, plugins: string[]) {
        const token = await this.login();
        if (!token.token) {
            throw new InternalServerErrorException('Login failed');
        }
        return axios
            .post(
                `${this.API_URL}deploy`,
                {
                    domain_name: domain,
                    bundle_id: this.BUNDLE_ID,
                    plugins,
                },
                {
                    headers: {
                        Authorization: `Bearer ${token.token}`,
                        'Content-Type': 'application/json',
                    },
                }
            )
            .then((response) => {
                return response.data;
            })
            .catch((error) => {
                throw new InternalServerErrorException(
                    error.response?.data?.message || error.message || 'Unknown error'
                );
            });
    }

    async delete(domain: string) {
        const token = await this.login();
        if (!token.token) {
            throw new InternalServerErrorException('Login failed');
        }
        axios
            .post(
                `${this.API_URL}delete`,
                {
                    domain_name: domain,
                    confirm: 'true',
                },
                {
                    headers: {
                        Authorization: `Bearer ${token.token}`,
                        'Content-Type': 'application/json',
                    },
                }
            )
            .then((response) => {
                return response.data;
            })
            .catch((error) => {
                throw new InternalServerErrorException(
                    error.response?.data?.message || error.message || 'Unknown error'
                );
            });
    }

    async checkStatus(domain: string) {
        const token = await this.login();
        if (!token.token) {
            throw new InternalServerErrorException('Login failed');
        }
        return axios
            .get(`${this.API_URL}status/${domain}`, {
                headers: {
                    Authorization: `Bearer ${token.token}`,
                    'Content-Type': 'application/json',
                },
            })
            .then((response) => {
                return response.data;
            })
            .catch((error) => {
                throw new InternalServerErrorException(
                    error.response?.data?.message || error.message || 'Unknown error'
                );
            });
    }

    async wpcliAsync(domain: string, command: string) {
        const token = await this.login();
        if (!token.token) {
            throw new InternalServerErrorException('Login failed');
        }
        return axios
            .post(
                `${this.API_URL}wpcli`,
                {
                    domain_name: domain,
                    command,
                },
                {
                    headers: {
                        Authorization: `Bearer ${token.token}`,
                        'Content-Type': 'application/json',
                    },
                }
            )
            .then((response) => {
                return response.data;
            })
            .catch((error) => {
                throw new InternalServerErrorException(
                    error.response?.data?.message || error.message || 'Unknown error'
                );
            });
    }

    async wpcli(domain: string, command: string) {
        const token = await this.login();
        if (!token.token) {
            throw new InternalServerErrorException('Login failed');
        }
        axios
            .post(
                `${this.API_URL}wpcli`,
                {
                    domain_name: domain,
                    command,
                },
                {
                    headers: {
                        Authorization: `Bearer ${token.token}`,
                        'Content-Type': 'application/json',
                    },
                }
            )
            .then((response) => {
                return response.data;
            })
            .catch((error) => {
                throw new InternalServerErrorException(
                    error.response?.data?.message || error.message || 'Unknown error'
                );
            });
    }
}
