import { Field, InputType } from '@nestjs/graphql';
import { IsArray, IsOptional, IsString, IsNumber, IsIn, ValidateNested, ArrayMinSize } from 'class-validator';
import { Type } from 'class-transformer';

@InputType()
export class AIMessageInput {
    @Field()
    @IsString()
    @IsIn(['system', 'user', 'assistant'])
    role: 'system' | 'user' | 'assistant';

    @Field()
    @IsString()
    content: string;
}

@InputType()
export class ChatCompletionInput {
    @Field(() => [AIMessageInput])
    @IsArray()
    @ArrayMinSize(1)
    @ValidateNested({ each: true })
    @Type(() => AIMessageInput)
    messages: AIMessageInput[];

    @Field({ nullable: true, defaultValue: 'llama-3.2-3b' })
    @IsOptional()
    @IsString()
    model?: string;

    @Field({ nullable: true, defaultValue: 0.4 })
    @IsOptional()
    @IsNumber()
    temperature?: number;

    @Field({ nullable: true, defaultValue: 1 })
    @IsOptional()
    @IsNumber()
    top_p?: number;

    @Field({ nullable: true, defaultValue: 800 })
    @IsOptional()
    @IsNumber()
    max_tokens?: number;
}
