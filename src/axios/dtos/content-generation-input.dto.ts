import { Field, InputType } from '@nestjs/graphql';
import { IsArray, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import Graph<PERSON><PERSON>SON from 'graphql-type-json';

@InputType()
export class FAQInput {
    @Field()
    @IsString()
    question: string;

    @Field()
    @IsString()
    answer: string;
}

@InputType()
export class InputFieldInput {
    @Field()
    @IsString()
    item: string;

    @Field()
    @IsString()
    tooltip: string;

    @Field()
    @IsString()
    type: string;
}

@InputType()
export class ContentGenerationInput {
    @Field(() => [FAQInput])
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => FAQInput)
    faqs: FAQInput[];

    @Field(() => [InputFieldInput])
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => InputFieldInput)
    inputFields: InputFieldInput[];
}
