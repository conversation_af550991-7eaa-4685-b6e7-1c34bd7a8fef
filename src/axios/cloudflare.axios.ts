import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import appConf from '../configs/app.conf';
import { CreateDnsRecordInput, UpdateDnsRecordInput, DnsRecord } from '../modules/cloudflare/dtos/dns-record.dto';
import { CreateZoneInput, Zone, DeleteZoneResponse } from '../modules/cloudflare/dtos/zone.dto';

export interface CloudflareZoneResponse {
    result: {
        id: string;
        name: string;
        name_servers: string[];
        status: string;
        paused: boolean;
        type: string;
        development_mode: number;
        original_name_servers: string[];
        original_registrar: string;
        original_dnshost: string;
        modified_on: string;
        created_on: string;
        activated_on: string;
        meta: {
            step: number;
            custom_certificate_quota: number;
            page_rule_quota: number;
            phishing_detected: boolean;
        };
        owner: {
            id: string;
            type: string;
            email: string;
        };
    };
    success: boolean;
    errors: any[];
    messages: any[];
}

@Injectable()
export class CloudflareAxios {
    private readonly API_TOKEN = appConf.CF_API_TOKEN;
    private readonly ZONE_ID = appConf.CF_ZONE_ID;
    private readonly BASE_URL = 'https://api.cloudflare.com/client/v4';
    private client: AxiosInstance;

    constructor() {
        this.client = axios.create({
            baseURL: this.BASE_URL,
            headers: this.getHeaders(),
        });
    }

    private getHeaders() {
        return {
            'X-Auth-Key': `57c497ca89967c064872e07391427cf5d1043`,
            'X-Auth-Email': '<EMAIL>',
            'Content-Type': 'application/json',
        };
    }

    private formatDnsRecord(recordResult: any, zone: CloudflareZoneResponse): DnsRecord {
        return {
            id: recordResult.id,
            type: recordResult.type,
            name: recordResult.name,
            content: recordResult.content,
            ttl: recordResult.ttl,
            priority: recordResult.priority,
            proxied: recordResult.proxied,
            zone_id: zone.result.id,
            zone_name: zone.result.name,
            created_on: recordResult.created_on,
            modified_on: recordResult.modified_on,
        };
    }

    async getZoneByName(domainName: string): Promise<CloudflareZoneResponse> {
        try {
            const url = `${this.BASE_URL}/zones`;
            console.log(`Calling Cloudflare API to get zone info for domain: ${domainName}`);

            const response = await axios.get(url, {
                headers: this.getHeaders(),
                params: {
                    name: domainName,
                },
            });

            if (!response.data.success) {
                throw new HttpException(response.data.errors || 'Error calling Cloudflare API', HttpStatus.BAD_REQUEST);
            }

            if (response.data.result.length === 0) {
                throw new HttpException(`Domain ${domainName} not found in Cloudflare`, HttpStatus.NOT_FOUND);
            }

            // Return the first result with name_servers
            return {
                result: response.data.result[0],
                success: response.data.success,
                errors: response.data.errors,
                messages: response.data.messages,
            };
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }

            console.error('Cloudflare API Error:', error.response?.data || error.message);
            throw new HttpException(
                error.response?.data || 'Error calling Cloudflare API',
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async getNameServers(domainName: string): Promise<string[]> {
        try {
            const zoneInfo = await this.getZoneByName(domainName);
            return zoneInfo.result.name_servers || [];
        } catch (error) {
            throw error;
        }
    }

    // DNS Record CRUD operations
    async getDnsRecords(domain: string): Promise<DnsRecord[]> {
        const zone = await this.getZoneByName(domain);
        if (!zone.result) {
            throw new HttpException(`Zone not found for domain: ${domain}`, HttpStatus.NOT_FOUND);
        }

        const response = await this.client.get(`/zones/${zone.result.id}/dns_records`);

        if (!response.data.success) {
            throw new HttpException(response.data.errors || 'Error fetching DNS records', HttpStatus.BAD_REQUEST);
        }

        return response.data.result.map((e) => this.formatDnsRecord(e, zone));
    }

    async getDnsRecord(domain: string, recordId: string): Promise<DnsRecord> {
        const zone = await this.getZoneByName(domain);
        if (!zone.result) {
            throw new HttpException(`Zone not found for domain: ${domain}`, HttpStatus.NOT_FOUND);
        }

        const response = await this.client.get(`/zones/${zone.result.id}/dns_records/${recordId}`);

        if (!response.data.success) {
            throw new HttpException(response.data.errors || 'Error fetching DNS record', HttpStatus.BAD_REQUEST);
        }

        return this.formatDnsRecord(response.data.result, zone);
    }

    async createDnsRecord(input: CreateDnsRecordInput): Promise<DnsRecord> {
        const zone = await this.getZoneByName(input.domain);
        if (!zone.result) {
            throw new HttpException(`Zone not found for domain: ${input.domain}`, HttpStatus.NOT_FOUND);
        }

        try {
            const recordData = {
                type: input.type,
                name: input.name,
                content: input.content,
                ttl: input.ttl || 120,
                ...(input.priority && { priority: input.priority }),
                proxied: input.proxied || false,
            };

            const response = await this.client.post(`/zones/${zone.result.id}/dns_records`, recordData);

            if (!response.data.success) {
                throw new HttpException(response.data.errors || 'Error creating DNS record', HttpStatus.BAD_REQUEST);
            }

            return this.formatDnsRecord(response.data.result, zone);
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }

            console.error('Cloudflare API Error:', error.response?.data || error.message);
            throw new HttpException(
                error.response?.data || 'Error calling Cloudflare API',
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async updateDnsRecord(input: UpdateDnsRecordInput): Promise<DnsRecord> {
        const zone = await this.getZoneByName(input.domain);
        if (!zone.result) {
            throw new HttpException(`Zone not found for domain: ${input.domain}`, HttpStatus.NOT_FOUND);
        }

        const updateData: any = {};
        if (input.type) updateData.type = input.type;
        if (input.name) updateData.name = input.name;
        if (input.content) updateData.content = input.content;
        if (input.ttl) updateData.ttl = input.ttl;
        if (input.priority !== undefined) updateData.priority = input.priority;
        if (input.proxied !== undefined) updateData.proxied = input.proxied;

        const response = await this.client.put(`/zones/${zone.result.id}/dns_records/${input.recordId}`, updateData);

        if (!response.data.success) {
            throw new HttpException(response.data.errors || 'Error updating DNS record', HttpStatus.BAD_REQUEST);
        }

        return this.formatDnsRecord(response.data.result, zone);
    }

    async deleteDnsRecord(domain: string, recordId: string): Promise<boolean> {
        const zone = await this.getZoneByName(domain);
        if (!zone.result) {
            throw new HttpException(`Zone not found for domain: ${domain}`, HttpStatus.NOT_FOUND);
        }

        const response = await this.client.delete(`/zones/${zone.result.id}/dns_records/${recordId}`);

        if (!response.data.success) {
            throw new HttpException(response.data.errors || 'Error deleting DNS record', HttpStatus.BAD_REQUEST);
        }

        return response.data.success;
    }

    async createZone(input: CreateZoneInput): Promise<Zone> {
        try {
            const zoneData = {
                name: input.name,
                type: input.type || 'full',
                jump_start: input.jump_start || false,
            };

            const response = await this.client.post('/zones', zoneData);

            if (!response.data.success) {
                throw new HttpException(response.data.errors || 'Error creating zone', HttpStatus.BAD_REQUEST);
            }

            return this.formatZone(response.data.result);
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }

            console.error('Cloudflare API Error:', error.response?.data || error.message);
            throw new HttpException(
                error.response?.data || 'Error calling Cloudflare API',
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async deleteZone(zoneId: string): Promise<DeleteZoneResponse> {
        try {
            const response = await this.client.delete(`/zones/${zoneId}`);

            if (!response.data.success) {
                throw new HttpException(response.data.errors || 'Error deleting zone', HttpStatus.BAD_REQUEST);
            }

            return {
                success: response.data.success,
                message: 'Zone deleted successfully',
            };
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }

            console.error('Cloudflare API Error:', error.response?.data || error.message);
            throw new HttpException(
                error.response?.data || 'Error calling Cloudflare API',
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    async deleteZoneByName(domainName: string): Promise<DeleteZoneResponse> {
        try {
            const zone = await this.getZoneByName(domainName);
            if (!zone.result) {
                throw new HttpException(`Zone not found for domain: ${domainName}`, HttpStatus.NOT_FOUND);
            }

            return await this.deleteZone(zone.result.id);
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }

            console.error('Error deleting zone by name:', error.message);
            throw new HttpException('Error deleting zone', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private formatZone(zoneResult: any): Zone {
        return {
            id: zoneResult.id,
            name: zoneResult.name,
            name_servers: zoneResult.name_servers || [],
            status: zoneResult.status,
            paused: zoneResult.paused,
            type: zoneResult.type,
            development_mode: zoneResult.development_mode || 0,
            original_name_servers: zoneResult.original_name_servers || [],
            original_registrar: zoneResult.original_registrar,
            original_dnshost: zoneResult.original_dnshost,
            modified_on: zoneResult.modified_on,
            created_on: zoneResult.created_on,
            activated_on: zoneResult.activated_on,
            account: {
                id: zoneResult.account?.id || '',
                name: zoneResult.account?.name || '',
            },
            meta: {
                step: zoneResult.meta?.step || 0,
                custom_certificate_quota: zoneResult.meta?.custom_certificate_quota || 0,
                page_rule_quota: zoneResult.meta?.page_rule_quota || 0,
                phishing_detected: zoneResult.meta?.phishing_detected || false,
                cdn_only: zoneResult.meta?.cdn_only,
                dns_only: zoneResult.meta?.dns_only,
                wildcard_proxiable: zoneResult.meta?.wildcard_proxiable,
                multiple_railguns_allowed: zoneResult.meta?.multiple_railguns_allowed,
            },
            owner: {
                id: zoneResult.owner?.id || '',
                type: zoneResult.owner?.type || '',
                name: zoneResult.owner?.name,
                email: zoneResult.owner?.email,
            },
            plan: zoneResult.plan
                ? {
                      id: zoneResult.plan.id,
                      name: zoneResult.plan.name,
                      price: zoneResult.plan.price,
                      currency: zoneResult.plan.currency,
                      frequency: zoneResult.plan.frequency,
                      is_subscribed: zoneResult.plan.is_subscribed,
                      can_subscribe: zoneResult.plan.can_subscribe,
                      legacy_id: zoneResult.plan.legacy_id,
                      legacy_discount: zoneResult.plan.legacy_discount,
                      externally_managed: zoneResult.plan.externally_managed,
                  }
                : undefined,
            cname_suffix: zoneResult.cname_suffix,
            permissions: zoneResult.permissions,
            tenant: zoneResult.tenant
                ? {
                      id: zoneResult.tenant.id,
                      name: zoneResult.tenant.name,
                  }
                : undefined,
            tenant_unit: zoneResult.tenant_unit
                ? {
                      id: zoneResult.tenant_unit.id,
                  }
                : undefined,
            vanity_name_servers: zoneResult.vanity_name_servers,
            verification_key: zoneResult.verification_key,
        };
    }
}
