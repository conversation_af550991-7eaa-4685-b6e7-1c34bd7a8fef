import { Field, ObjectType } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';

@ObjectType()
export class AIMessageModel {
    @Field()
    role: string;

    @Field()
    content: string;
}

@ObjectType()
export class AIResponseModel {
    @Field()
    success: boolean;

    @Field(() => GraphQLJSON, { nullable: true })
    data?: any;

    @Field({ nullable: true })
    message?: string;
}
