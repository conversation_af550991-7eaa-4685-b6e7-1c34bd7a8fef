import { Injectable, InternalServerErrorException } from '@nestjs/common';
import axios from 'axios';

export interface IAIResponse {
    success: boolean;
    data?: any;
    message?: string;
}

export interface IAIMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
}

export interface IAIChatRequest {
    model: string;
    temperature: number;
    top_p: number;
    max_tokens: number;
    messages: IAIMessage[];
}

@Injectable()
export class AIAxios {
    private readonly URL_AI = process.env.AI_API_URL || 'https://openrouter.ai/api';
    private readonly API_KEY = process.env.AI_API_KEY;
    private readonly REQUEST_TIMEOUT = 100 * 1000;

    constructor() {}

    async chatCompletion(
        messages: IAIMessage[],
        model: string = 'meta-llama/llama-3.2-3b-instruct',
        temperature: number = 0.4,
        topP: number = 1,
        maxTokens: number = 800
    ): Promise<IAIResponse> {
        try {
            // Validate messages parameter
            if (!messages || !Array.isArray(messages) || messages.length === 0) {
                throw new Error('Messages parameter is required and must be a non-empty array');
            }

            const requestData: IAIChatRequest = {
                model,
                temperature,
                top_p: topP,
                max_tokens: maxTokens,
                messages,
            };

            console.log('AI Request URL:', `${this.URL_AI}/v1/chat/completions`);
            console.log('AI Request Data:', JSON.stringify(requestData, null, 2));

            const response = await axios.post(`${this.URL_AI}/v1/chat/completions`, requestData, {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${this.API_KEY}`,
                },
                timeout: this.REQUEST_TIMEOUT,
            });

            console.log('AI Response Data:', response.data);

            return { success: true, data: response.data };
        } catch (error) {
            console.error('AI Request Error:', error.response?.data || error.message || 'Unknown error');
            console.error('Error details:', {
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data,
            });
            throw new InternalServerErrorException(
                error.response?.data?.message || error.message || 'AI chat completion failed'
            );
        }
    }

    /**
     * Parse AI content generation response into structured array
     * @param aiResponseContent Raw AI response content
     * @returns Array of parsed content items
     */
    parseContentGenerationResponse(aiResponseContent: string): Array<{
        item: string;
        tooltip: string;
        type: string;
        ai_value: string;
    }> {
        if (!aiResponseContent || typeof aiResponseContent !== 'string') {
            throw new Error('Invalid AI response content');
        }

        const lines = aiResponseContent.split('\n').filter((line) => line.trim().length > 0);

        const parsedItems = [];

        for (const line of lines) {
            const parts = line.split(' | ').map((part) => part.trim());

            if (parts.length === 4) {
                // @ts-ignore
                parsedItems.push({
                    item: parts[0],
                    tooltip: parts[1],
                    type: parts[2],
                    ai_value: parts[3],
                });
            } else {
                console.warn('Skipping malformed line:', line);
            }
        }

        return parsedItems;
    }

    async generateContentCompletion(faqs: any[], inputFields: any[]): Promise<IAIResponse> {
        try {
            const systemMessage = {
                role: 'system' as const,
                content:
                    "You are an expert website content copywriter who writes in an ornate, lyrical style suitable for modern web pages.\n\nYou will be given a JSON payload with two arrays:\n1. 'faqs': an array of objects, each with 'question' and 'answer' keys describing the brand.\n2. 'input_fields': an array of objects, each with 'label' and 'type'.\n\nFor every object in 'input_fields':\n- Derive these four pieces of information **in this exact order**:\n  1. **item** – the text from 'label'.\n  2. **tooltip** – the phrase \"Add your \" followed by the lower-case form of the item (e.g., \"Add your main heading\").\n  3. **type** – copy from the original 'type'.\n  4. **ai_value** – creative copy that:\n     • Reflects the brand information in 'faqs' (company description, industry, services, audience, usage context, desired writing style).\n     • Matches the semantic intent of the item:\n       ▪ *Main Heading* → a 4-7-word headline capturing the brand essence.\n       ▪ **Submit button (type === 'button')** → **EXACTLY 2-3 words maximum, Title-Case call-to-action** (e.g., \"Get Started\", \"Learn More\", \"Contact Us\"). **DO NOT write sentences or descriptions for buttons - only the actual button text!**\n       ▪ *Hero content* → a 1-2-sentence hero paragraph (≤ 35 words) evoking emotion and value.\n     • Uses elaborate imagery and poetic diction while remaining clear and professional.\n     • **CRITICAL: For buttons, ignore the ornate style and provide ONLY short, actionable button text - never descriptions or long phrases.**\n\n**Output format (plain text only):**\nReturn one line per item.\nOn each line, concatenate the four pieces (item, tooltip, type, ai_value) separated by the pipe symbol **\" | \"** with single spaces around it.\nDo **not** wrap the result in JSON, arrays, objects, markdown, or code fences. Produce only these newline-separated strings, in the same order as 'input_fields'.\n\nBegin when ready.",
            };

            const userContent = JSON.stringify({
                faqs,
                input_fields: inputFields,
            });

            const userMessage = {
                role: 'user' as const,
                content: userContent,
            };

            const requestData: IAIChatRequest = {
                model: 'meta-llama/llama-3.2-3b-instruct',
                temperature: 0.4,
                top_p: 1,
                max_tokens: 800,
                messages: [systemMessage, userMessage],
            };

            console.log('AI Content Generation Request URL:', `${this.URL_AI}/v1/chat/completions`);
            console.log('AI Content Generation Request Data:', JSON.stringify(requestData, null, 2));

            const response = await axios.post(`${this.URL_AI}/v1/chat/completions`, requestData, {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${this.API_KEY}`,
                },
                timeout: this.REQUEST_TIMEOUT,
            });

            console.log('AI Content Generation Response Data:', response.data);

            return { success: true, data: response.data };
        } catch (error) {
            console.error(
                'AI Content Generation Request Error:',
                error.response?.data || error.message || 'Unknown error'
            );
            console.error('Error details:', {
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data,
            });
            throw new InternalServerErrorException(
                error.response?.data?.message || error.message || 'AI content generation failed'
            );
        }
    }
}
