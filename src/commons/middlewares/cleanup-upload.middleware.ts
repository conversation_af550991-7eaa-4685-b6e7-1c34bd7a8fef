import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class CleanupUploadMiddleware implements NestMiddleware {
    use(req: Request, res: Response, next: NextFunction) {
        const onClose = () => {
            // Nếu client disconnect, clean file
            if (!res.headersSent && req['uploadedFiles']) {
                for (const file of req['uploadedFiles']) {
                    if (!fs.existsSync(file.destination)) continue;
                    const filePath = path.join(file.destination, file.filename);
                    fs.unlink(filePath, (err) => {
                        if (err) console.warn('Failed to delete interrupted file:', filePath);
                    });
                }
            }
        };

        req.on('close', onClose);
        req.on('error', onClose);
        res.on('finish', () => {
            // Request hoàn thành thành công → không xoá
            req['uploadedFiles'] = null;
        });

        next();
    }
}
