import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import appConf from '../../configs/app.conf';
import { GqlExecutionContext } from '@nestjs/graphql';

@Injectable()
export class WebhookTokenGuard implements CanActivate {
    canActivate(context: ExecutionContext): boolean {
        const ctx = GqlExecutionContext.create(context);
        const request = ctx.getContext().req;

        const authHeader = request.headers['authorization'];
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new UnauthorizedException('Missing or invalid Authorization header');
        }

        const token = authHeader.replace('Bearer ', '').trim();
        const expectedToken = appConf.W_API_KEY;

        if (token !== expectedToken) {
            throw new UnauthorizedException('Invalid API token');
        }

        return true;
    }
}
