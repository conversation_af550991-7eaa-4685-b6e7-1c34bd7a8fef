import { Call<PERSON><PERSON>ler, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class AssignIdToBodyInterceptor implements NestInterceptor {
    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const gqlContext = GqlExecutionContext.create(context);
        const args = gqlContext.getArgs();
        const body = args?.body;
        const id = args?.id;

        if (!id || !body || typeof body !== 'object') {
            return next.handle();
        }
        // Gán id vào body
        body.id = args.id;

        return next.handle().pipe(map((data) => data));
    }
}
