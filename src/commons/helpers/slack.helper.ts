import axios from 'axios';
import appConf from '../../configs/app.conf';

/**
 * <PERSON><PERSON><PERSON> thông báo lỗi đến Slack
 * @param exception Lỗi cần gửi
 * @param request Request object (c<PERSON> thể null nếu là GraphQL)
 * @param source Nguồn của lỗi (REST API, GraphQL, etc.)
 */
export const sendErrorToSlack = async (
    exception: any,
    request: any = null,
    source: string = 'Unknown'
): Promise<void> => {
    try {
        const timestamp = new Date().toISOString();

        // Thông tin từ request (nếu có)
        let requestInfo = '';
        if (request) {
            const url = request.originalUrl || request.url;
            const method = request.method;
            const ip = request.ip || request.connection?.remoteAddress;
            requestInfo = `\nURL: ${method} ${url}\nIP: ${ip || 'N/A'}`;
        }

        // Tạo message theo đúng định dạng yêu cầu của Slack
        const payload = JSON.stringify({
            text: `🚨 INTERNAL SERVER ERROR (${source}) 🚨\nTime: ${timestamp}${requestInfo}\nError: ${exception.message}\nStack: ${exception.stack?.substring(0, 300) || 'No stack trace'}`,
        });

        console.log(`Sending error log to Slack from ${source}`);

        // Sử dụng cú pháp giống với curl command đã hoạt động
        // TODO:
        // const response = await axios({
        //     method: 'post',
        //     url: appConf.SLACK_WEBHOOK_URL,
        //     headers: { 'Content-Type': 'application/json' },
        //     data: payload,
        // });

        //console.log(`Error log sent to Slack successfully. Status: ${response.status}`);
    } catch (error) {
        console.log(`Failed to send error log to Slack: ${error.message}`);
        if (error.response) {
            console.log(`Response status: ${error.response.status}, data: ${JSON.stringify(error.response.data)}`);
        } else if (error.request) {
            console.log('No response received from Slack');
        }
    }
};
