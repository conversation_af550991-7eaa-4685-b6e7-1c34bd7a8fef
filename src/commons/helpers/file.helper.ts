import * as fs from 'fs';
import * as path from 'path';
import { URL } from 'url';
import { tmpdir } from 'os';
import appConf from '../../configs/app.conf';
import axios from 'axios';

export const CONTROLLER_PREFIX = 'files';
export const UPLOAD_DIR = 'uploads';
export const IMAGES_PATH = 'images';
export const OTHERS_PATH = 'others';

export const normalizePath = (path: string) => path.replace(/\\/g, '/');

export const convertUploadPath = (filePath: string) => {
    filePath = normalizePath(filePath);
    return filePath.replace(`${UPLOAD_DIR}/`, `${CONTROLLER_PREFIX}/`);
};

export const pathToUrl = (filePath: string) => appConf.API_URL + convertUploadPath(filePath);

export const downloadFileToTemp = async (fileUrl: string): Promise<string> => {
    const url = new URL(fileUrl);
    const ext = path.extname(url.pathname) || '.tmp';
    const tempFilePath = path.join(tmpdir(), `download-${Date.now()}${ext}`);

    const response = await axios.get(fileUrl, { responseType: 'stream' });

    await new Promise<void>((resolve, reject) => {
        const writer = fs.createWriteStream(tempFilePath);
        response.data.pipe(writer);

        writer.on('finish', () => resolve());
        writer.on('error', (err) => {
            // Xóa file nếu ghi bị lỗi
            fs.unlink(tempFilePath, () => reject(err));
        });
    });

    return tempFilePath;
};
