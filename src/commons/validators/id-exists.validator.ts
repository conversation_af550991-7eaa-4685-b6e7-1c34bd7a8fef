import {
    registerDecorator,
    ValidationArguments,
    ValidationOptions,
    ValidatorConstraint,
    ValidatorConstraintInterface,
} from 'class-validator';
import { Injectable } from '@nestjs/common';
import { AuthService } from '../../modules/auth/services/auth.service';

@ValidatorConstraint({ async: true })
@Injectable()
export class IdExistsConstraint implements ValidatorConstraintInterface {
    constructor(private readonly authService: AuthService) {}

    async validate(value: any, args: ValidationArguments): Promise<boolean> {
        const [entityConstraint] = args.constraints;
        if (value === undefined || entityConstraint === undefined) {
            return false;
        }
        return this.authService.repo.manager
            .createQueryBuilder()
            .from(entityConstraint, 'a')
            .where('id = :id', { id: value })
            .getExists();
    }
}

export function IdExists(table: Function | string, validationOptions?: ValidationOptions) {
    return function (object: Object, propertyName: string) {
        registerDecorator({
            name: 'idExists',
            target: object.constructor,
            propertyName,
            options: {
                ...validationOptions,
                message: validationOptions?.message || `The ${propertyName} does not exist in the ${table} table.`,
            },
            constraints: [table],
            validator: IdExistsConstraint,
        });
    };
}
