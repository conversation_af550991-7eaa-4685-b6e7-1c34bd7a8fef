import { ValidationArguments, ValidationOptions, registerDecorator } from 'class-validator';

export function MatchNumber(
    precision: number,
    scaleOrOptions?: number | ValidationOptions,
    validationOptions?: ValidationOptions
) {
    let scale: number | undefined;
    let options: ValidationOptions | undefined;

    if (typeof scaleOrOptions === 'object') {
        options = scaleOrOptions;
    } else {
        scale = scaleOrOptions;
        options = validationOptions;
    }

    return function (object: Object, propertyName: string) {
        registerDecorator({
            name: 'matchNumber',
            target: object.constructor,
            propertyName,
            options: {
                ...options,
                message:
                    options?.message ||
                    `$property must be a number with precision ${precision}${scale ? ` and scale ${scale}` : ''}`,
            },
            constraints: [precision, scale],
            validator: {
                validate(value: any, args: ValidationArguments) {
                    const [precision, scale] = args.constraints;
                    if (typeof value !== 'number') return false;

                    const [integerPart, decimalPart] = value.toString().split('.');

                    return integerPart.length <= precision && (!decimalPart || !scale || decimalPart.length <= scale);
                },
            },
        });
    };
}
