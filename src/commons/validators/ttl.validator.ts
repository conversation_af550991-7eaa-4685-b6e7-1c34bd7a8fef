import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

export function IsTTL(validationOptions?: ValidationOptions) {
    return function (object: Object, propertyName: string) {
        registerDecorator({
            name: 'isTTL',
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            validator: {
                validate(value: any) {
                    if (value === undefined || value === null) {
                        return true;
                    }

                    if (typeof value !== 'number') {
                        return false;
                    }

                    return value === 1 || (value >= 60 && value <= 86400);
                },
                defaultMessage(args: ValidationArguments) {
                    return `${args.property} must be 1 (Automatic) or between 60 and 86400 seconds`;
                },
            },
        });
    };
}
