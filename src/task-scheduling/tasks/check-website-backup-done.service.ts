import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DataSource } from 'typeorm';
import { LoggerService } from '../../loggers/logger.service';
import { BackupWebsiteEntity } from '../../entities/backup-website.entity';
import { WebsiteEntity } from '../../entities/website.entity';
import axios from 'axios';
import appConf from '../../configs/app.conf';

interface BackupResponse {
    timestamp: string;
    file_id: string;
    backup_uploaded: boolean;
    backup_deleted: boolean;
}

@Injectable()
export class CheckWebsiteBackupDoneService {
    constructor(
        private readonly dataSource: DataSource,
        private readonly loggerService: LoggerService
    ) {}

    @Cron(CronExpression.EVERY_5_MINUTES)
    async handleCron() {
        try {
            this.loggerService.log(
                'CheckWebsiteBackupDone: Starting backup completion check...',
                'CheckWebsiteBackupDone'
            );

            // Fetch all records from backup_websites where:
            // - file_id IS NULL
            // - is_request = true
            // - and the related website.is_process_backup = true
            const backupRecords = await this.dataSource.manager
                .getRepository(BackupWebsiteEntity)
                .createQueryBuilder('backup')
                .leftJoinAndSelect('backup.website', 'website')
                .where('backup.file_id IS NULL')
                .andWhere('backup.is_request = :isRequest', { isRequest: true })
                .andWhere('website.is_process_backup = :isProcessBackup', { isProcessBackup: true })
                .getMany();

            if (!backupRecords.length) {
                this.loggerService.log(
                    'CheckWebsiteBackupDone: No backup records found to check',
                    'CheckWebsiteBackupDone'
                );
                return;
            }

            this.loggerService.log(
                `CheckWebsiteBackupDone: Found ${backupRecords.length} backup records to check`,
                'CheckWebsiteBackupDone'
            );

            // Process each backup record
            for (const backupRecord of backupRecords) {
                try {
                    //await this.checkBackupCompletion(backupRecord);
                    this.checkBackupCompletion(backupRecord);
                } catch (error) {
                    this.loggerService.error(
                        `CheckWebsiteBackupDone: Failed to check backup completion for record ${backupRecord.id}: ${error.message}`,
                        'CheckWebsiteBackupDone'
                    );
                }
            }

            this.loggerService.log(
                'CheckWebsiteBackupDone: Backup completion check completed',
                'CheckWebsiteBackupDone'
            );
        } catch (error) {
            this.loggerService.error(
                `CheckWebsiteBackupDone: Error in cron job: ${error.message}`,
                'CheckWebsiteBackupDone'
            );
        }
    }

    private async checkBackupCompletion(backupRecord: BackupWebsiteEntity): Promise<void> {
        const website = backupRecord.website;
        const domain = website.domain;

        if (!domain) {
            this.loggerService.error(
                `CheckWebsiteBackupDone: No domain found for backup record ${backupRecord.id}`,
                'CheckWebsiteBackupDone'
            );
            return;
        }

        try {
            this.loggerService.log(
                `CheckWebsiteBackupDone: Checking backup completion for domain: ${domain}`,
                'CheckWebsiteBackupDone'
            );

            // Make POST request to check backup completion
            const response = await axios.post(
                `https://${domain}/wp-json/customer-edit/v1/backup-site`,
                {},
                {
                    headers: {
                        Authorization: `Bearer ${appConf.W_API_KEY}`,
                        'Content-Type': 'application/json',
                    },
                    timeout: 60000 * 5, // 5 minute timeout
                }
            );

            const backupData: BackupResponse = response.data;

            this.loggerService.log(
                `CheckWebsiteBackupDone: Backup API response for domain ${domain}: ${JSON.stringify(backupData)}`,
                'CheckWebsiteBackupDone'
            );

            // Check if the response indicates successful backup completion
            if (backupData.backup_uploaded && backupData.backup_deleted && backupData.file_id) {
                await this.updateBackupCompletion(backupRecord, backupData);
            } else {
                this.loggerService.log(
                    `CheckWebsiteBackupDone: Backup not yet completed for domain ${domain}`,
                    'CheckWebsiteBackupDone'
                );
            }
        } catch (error) {
            this.loggerService.error(
                `CheckWebsiteBackupDone: Failed to check backup completion for domain ${domain}: ${error.message}`,
                'CheckWebsiteBackupDone'
            );
        }
    }

    private async updateBackupCompletion(backupRecord: BackupWebsiteEntity, backupData: BackupResponse): Promise<void> {
        // Parse timestamp from the response
        const updatedAt = new Date(backupData.timestamp);

        // Update file_id and updated_at in the backup_websites record
        await this.dataSource.manager.update(
            BackupWebsiteEntity,
            { id: backupRecord.id },
            {
                file_id: parseInt(backupData.file_id),
                updated_at: updatedAt,
            }
        );

        // Update website.is_process_backup = false
        await this.dataSource.manager.update(
            WebsiteEntity,
            { id: backupRecord.website_id },
            {
                is_process_backup: false,
            }
        );

        this.loggerService.log(
            `CheckWebsiteBackupDone: Successfully updated backup record ${backupRecord.id} with file_id ${backupData.file_id} and set website.is_process_backup = false`,
            'CheckWebsiteBackupDone'
        );
    }
}
