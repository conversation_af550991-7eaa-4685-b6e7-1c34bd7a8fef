import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DataSource, In } from 'typeorm';
import { WebsiteStatus } from '../../entities/website.entity';
import { LoggerService } from '../../loggers/logger.service';
import { CronTemplateEntity } from '../../entities/cron-template.entity';
import { ReviewsieuxeAxios } from '../../axios/reviewsieuxe.axios';
import { appConf } from '../../configs/app.conf';

@Injectable()
export class DeployWaitingCronTemplatesService {
    constructor(
        private readonly dataSource: DataSource,
        private readonly loggerService: LoggerService,
        private readonly rsAxios: ReviewsieuxeAxios
    ) {}

    @Cron(CronExpression.EVERY_MINUTE)
    async handleCron() {
        //for customer
        const falsePlugins = [
            'elementor',
            'all-in-one-wp-migration',
            `${appConf.API_URL}files/others/plugins/elementor-pro-3.28.3.zip`,
            `${appConf.API_URL}files/others/plugins/all-in-one-wp-migration-unlimited-extension.zip`,
            `${appConf.API_URL}files/others/plugins/ipt_template_toolkit-24062025.zip`,
            `${appConf.API_URL}files/others/plugins/ipt-user-sync.26.03.2025.zip`,
        ];
        //for designer tagging
        const truePlugins = [
            'elementor',
            'all-in-one-wp-migration',
            `${appConf.API_URL}files/others/plugins/elementor-pro-3.28.3.zip`,
            `${appConf.API_URL}files/others/plugins/all-in-one-wp-migration-unlimited-extension.zip`,
            `${appConf.API_URL}files/others/plugins/ipt_template_toolkit-24062025.zip`,
            `${appConf.API_URL}files/others/plugins/ipt-user-sync.26.03.2025.zip`,
            `${appConf.API_URL}files/others/plugins/designer-tagging.24.06.2026-2.zip`,
        ];

        const waiting = await this.dataSource.manager
            .getRepository(CronTemplateEntity)
            .find({ where: { status_id: WebsiteStatus.WAITING }, relations: ['template'] });
        if (!waiting.length) {
            console.log('DeployWaitingCronTemplates: No waiting cron templates');
            this.loggerService.log(
                'DeployWaitingCronTemplates: No waiting cron templates',
                'DeployWaitingCronTemplates'
            );
        }
        await this.dataSource.manager
            .transaction(async (run) => {
                await run.update(
                    CronTemplateEntity,
                    {
                        id: In(waiting.map((w) => w.id)),
                    },
                    { status_id: WebsiteStatus.INSTALLING }
                );
                const promise: any[] = [];
                for (let cronTemplateEntity of waiting) {
                    const isKit = !cronTemplateEntity.template; // If there is no template, it is a kit
                    const plugins = isKit || cronTemplateEntity.template?.is_kit ? truePlugins : falsePlugins;
                    promise.push(this.rsAxios.deployAsync(cronTemplateEntity.domain, plugins));
                }
                await Promise.all(promise);
            })
            .catch((err) => {
                console.log('DeployWaitingCronTemplates: Error deploying => ', err);
                this.loggerService.log(
                    `DeployWaitingCronTemplates: Error deploying => ${JSON.stringify(err)}`,
                    'DeployWaitingCronTemplates'
                );
            })
            .then(() => {
                console.log('DeployWaitingCronTemplates => Deployed');
                this.loggerService.log('DeployWaitingCronTemplates => Deployed', 'DeployWaitingCronTemplates');
            });
    }
}
