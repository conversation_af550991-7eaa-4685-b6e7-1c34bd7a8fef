import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DataSource } from 'typeorm';
import { WebsiteStatus } from '../../entities/website.entity';
import { LoggerService } from '../../loggers/logger.service';
import { CronTemplateEntity } from '../../entities/cron-template.entity';
import { ReviewsieuxeAxios } from '../../axios/reviewsieuxe.axios';
import { TemplateComponentEntity } from '../../entities/template-component.entity';
import { ItemStatus } from '../../commons/enums.common';
import axios from 'axios';
import appConf from '../../configs/app.conf';
import { isEmpty } from 'lodash';

@Injectable()
export class CheckInstallingCronTemplatesService {
    constructor(
        private readonly dataSource: DataSource,
        private readonly loggerService: LoggerService,
        private readonly rsAxios: ReviewsieuxeAxios
    ) {}

    @Cron(CronExpression.EVERY_MINUTE)
    async handleCron() {
        const installing = await this.dataSource.manager.getRepository(CronTemplateEntity).find({
            where: { status_id: WebsiteStatus.INSTALLING },
            relations: ['template.templateFile', 'template.codeFile'],
        });
        if (!installing.length) {
            console.log('CheckInstallingCronTemplates: No waiting cron templates');
            this.loggerService.log(
                'CheckInstallingCronTemplates: No waiting cron templates',
                'CheckInstallingCronTemplates'
            );
        }
        const templateComponents = await this.dataSource.manager
            .getRepository(TemplateComponentEntity)
            .find({ where: { status_id: ItemStatus.ACTIVE }, relations: ['file'] });
        for (let cronTemplateEntity of installing) {
            await this.rsAxios
                .checkStatus(cronTemplateEntity.domain)
                .then(async (res: any) => {
                    try {
                        //JSON này vào cột deploy_status
                        await this.dataSource.manager.update(
                            CronTemplateEntity,
                            { id: cronTemplateEntity.id },
                            { deploy_status: res }
                        );
                        // Check if deployment has wp_password in the data array
                        const hasWpPassword = res?.success && !isEmpty(res?.wp_password);
                        if (hasWpPassword) {
                            //update status = instaled
                            await this.dataSource.manager.update(
                                CronTemplateEntity,
                                { id: cronTemplateEntity.id },
                                { status_id: WebsiteStatus.INSTALLED }
                            );
                            //call api wp
                            await this.rsAxios.wpcli(cronTemplateEntity.domain, 'option update blog_public 0');
                            await this.rsAxios.wpcli(
                                cronTemplateEntity.domain,
                                `user create designer_default <EMAIL> --role=designer --user_pass=${appConf.WP_ADMIN_PASS}`
                            );
                            // for designer tagging
                            if (cronTemplateEntity.template) {
                                if (cronTemplateEntity.template.is_kit) {
                                    await this.rsAxios.wpcli(
                                        cronTemplateEntity.domain,
                                        `elementor kit import ${cronTemplateEntity.template.templateFile?.file_url} --overrideConditions --sourceType=remote --user=user`
                                    );
                                    const promise: any[] = [];
                                    for (let templateComponent of templateComponents) {
                                        promise.push(
                                            axios.post(
                                                `https://${cronTemplateEntity.domain}/wp-json/template-toolkit/v1/elementor-templates/import`,
                                                {
                                                    file_url: templateComponent.file?.file_url,
                                                    template_type: 'auto',
                                                },
                                                {
                                                    headers: {
                                                        Authorization: `Bearer ${appConf.W_API_KEY}`,
                                                        'Content-Type': 'application/json',
                                                    },
                                                }
                                            )
                                        );
                                    }
                                    await Promise.all(promise);
                                    await this.dataSource.manager.update(
                                        CronTemplateEntity,
                                        { id: cronTemplateEntity.id },
                                        { status_id: WebsiteStatus.ACTIVE }
                                    );
                                } else {
                                    let url = '';
                                    if (cronTemplateEntity.template.codeFile?.file_url) {
                                        url = cronTemplateEntity.template.codeFile?.file_url;
                                    } else if (cronTemplateEntity.template.templateFile?.file_url) {
                                        url = cronTemplateEntity.template.templateFile?.file_url;
                                    } else {
                                        url = appConf.WP_FILE_URL_DEFAULT;
                                    }
                                    const importBackupRes = await axios.post(
                                        `https://${cronTemplateEntity.domain}/wp-json/template-toolkit/v1/import-backup`,
                                        {
                                            file_url: url,
                                        },
                                        {
                                            headers: {
                                                Authorization: `Bearer ${appConf.W_API_KEY}`,
                                                'Content-Type': 'application/json',
                                            },
                                        }
                                    );
                                    if (importBackupRes?.data?.job_id) {
                                        await this.dataSource.manager.update(
                                            CronTemplateEntity,
                                            { id: cronTemplateEntity.id },
                                            { job_id: importBackupRes.data.job_id }
                                        );
                                    }
                                }
                            } else {
                                const promise: any[] = [];
                                for (let templateComponent of templateComponents) {
                                    promise.push(
                                        axios.post(
                                            `https://${cronTemplateEntity.domain}/wp-json/template-toolkit/v1/elementor-templates/import`,
                                            {
                                                file_url: templateComponent.file?.file_url,
                                                template_type: 'auto',
                                            },
                                            {
                                                headers: {
                                                    Authorization: `Bearer ${appConf.W_API_KEY}`,
                                                    'Content-Type': 'application/json',
                                                },
                                            }
                                        )
                                    );
                                }
                                await Promise.all(promise);
                                await this.dataSource.manager.update(
                                    CronTemplateEntity,
                                    { id: cronTemplateEntity.id },
                                    { status_id: WebsiteStatus.ACTIVE }
                                );
                            }
                        }
                        this.loggerService.log(
                            'CheckInstallingCronTemplates => Deployed',
                            'CheckInstallingCronTemplates'
                        );
                    } catch (error) {
                        console.log('CheckInstallingCronTemplates: Error deploying => ', error);
                        this.loggerService.log(
                            `CheckInstallingCronTemplates: Error deploying => ${JSON.stringify(error)}`,
                            'CheckInstallingCronTemplates'
                        );
                    }
                })
                .catch((err) => {
                    console.log('CheckInstallingCronTemplates: Error deploying => ', err);
                    this.loggerService.log(
                        `CheckInstallingCronTemplates: Error deploying => ${JSON.stringify(err)}`,
                        'CheckInstallingCronTemplates'
                    );
                });
        }
    }
}
