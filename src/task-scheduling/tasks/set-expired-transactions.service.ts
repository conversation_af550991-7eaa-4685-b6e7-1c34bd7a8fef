import { Injectable } from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { DataSource, In, <PERSON>Than, <PERSON>Than, Not } from 'typeorm';
import { TransactionEntity, TransactionType } from '../../entities/transaction.entity';
import { WebsiteEntity, WebsiteStatus } from '../../entities/website.entity';
import { LoggerService } from '../../loggers/logger.service';
import { PlanType } from 'src/entities/subscription-plan.entity';
import { ReviewsieuxeAxios } from 'src/axios/reviewsieuxe.axios';

@Injectable()
export class SetExpiredTransactionsService {
    constructor(
        private readonly dataSource: DataSource,
        private readonly loggerService: LoggerService,
        private readonly rsAxios: ReviewsieuxeAxios
    ) {}

    @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
    async handleCron() {
        await this.dataSource.manager
            .transaction(async (run) => {
                const transactions = await run.findBy(TransactionEntity, {
                    type_id: TransactionType.WEBSITE,
                    end_date: Less<PERSON><PERSON>(new Date()),
                    website_id: MoreThan(0),
                    website: {
                        status_id: Not(WebsiteStatus.EXPIRED),
                    },
                });
                if (!transactions.length) {
                    console.log('SetExpiredTransactions: No expired transactions');
                    return;
                }

                for (const transaction of transactions) {
                    const transactionWithSameUserAndWebsite = transactions.find(
                        (t) =>
                            t.customer_id === transaction.customer_id &&
                            t.website_id === transaction.website_id &&
                            t.end_date > new Date()
                    );

                    if (!transactionWithSameUserAndWebsite && transaction?.plan?.type_id === PlanType.TRIAL) {
                        await this.rsAxios.delete(transaction.website?.domain || '');
                    }
                    if (!transactionWithSameUserAndWebsite) {
                        await run.update(
                            WebsiteEntity,
                            { id: transaction.website_id },
                            { status_id: WebsiteStatus.EXPIRED }
                        );
                    }
                }

                // const customerIds = transactions.map((transaction) => transaction.customer_id);
                // await run.update(
                //     UserEntity,
                //     {
                //         id: In([...new Set(customerIds)]),
                //     },
                //     { status_id: UserStatus.LOCKED }
                // );
            })
            .catch((err) => {
                console.log('SetExpiredTransactions: Error updating websites => ', err);
                this.loggerService.log(
                    `SetExpiredTransactions: Error updating websites => ${JSON.stringify(err)}`,
                    'SetExpiredTransactions'
                );
            })
            .then(() => {
                console.log('SetExpiredTransactions => Updated websites');
                this.loggerService.log('SetExpiredTransactions => Updated websites', 'SetExpiredTransactions');
            });
    }
}
