import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DataSource } from 'typeorm';
import { LoggerService } from '../../loggers/logger.service';
import { BackupWebsiteEntity } from '../../entities/backup-website.entity';
import { ReviewsieuxeAxios } from '../../axios/reviewsieuxe.axios';

@Injectable()
export class CheckWebsiteBackupService {
    constructor(
        private readonly dataSource: DataSource,
        private readonly loggerService: LoggerService,
        private readonly rsAxios: ReviewsieuxeAxios
    ) {}

    @Cron(CronExpression.EVERY_5_MINUTES)
    async handleCron() {
        try {
            this.loggerService.log('CheckWebsiteBackup: Starting backup check...', 'CheckWebsiteBackup');

            // Fetch all records from backup_websites where:
            // - file_id IS NULL
            // - is_request = false
            // - and the related website.is_process_backup = true
            const backupRecords = await this.dataSource.manager
                .getRepository(BackupWebsiteEntity)
                .createQueryBuilder('backup')
                .leftJoinAndSelect('backup.website', 'website')
                .where('backup.file_id IS NULL')
                .andWhere('backup.is_request = :isRequest', { isRequest: false })
                .andWhere('website.is_process_backup = :isProcessBackup', { isProcessBackup: true })
                .getMany();

            if (!backupRecords.length) {
                this.loggerService.log('CheckWebsiteBackup: No backup records found to process', 'CheckWebsiteBackup');
                return;
            }

            this.loggerService.log(
                `CheckWebsiteBackup: Found ${backupRecords.length} backup records to process`,
                'CheckWebsiteBackup'
            );

            // Process each backup record
            for (const backupRecord of backupRecords) {
                try {
                    //await this.processBackupRecord(backupRecord);
                    this.processBackupRecord(backupRecord);
                } catch (error) {
                    this.loggerService.error(
                        `CheckWebsiteBackup: Failed to process backup record ${backupRecord.id}: ${error.message}`,
                        'CheckWebsiteBackup'
                    );
                }
            }

            this.loggerService.log('CheckWebsiteBackup: Backup check completed', 'CheckWebsiteBackup');
        } catch (error) {
            this.loggerService.error(`CheckWebsiteBackup: Error in cron job: ${error.message}`, 'CheckWebsiteBackup');
        }
    }

    private async processBackupRecord(backupRecord: BackupWebsiteEntity): Promise<void> {
        const website = backupRecord.website;
        const domainName = website.domain;

        if (!domainName) {
            this.loggerService.error(
                `CheckWebsiteBackup: No domain found for backup record ${backupRecord.id}`,
                'CheckWebsiteBackup'
            );
            return;
        }

        try {
            this.loggerService.log(
                `CheckWebsiteBackup: Initiating backup for domain: ${domainName}`,
                'CheckWebsiteBackup'
            );

            // Make WP CLI API call using ReviewsieuxeAxios
            const response = await this.rsAxios.wpcliAsync(domainName, 'ai1wm backup');

            this.loggerService.log(
                `CheckWebsiteBackup: Backup API call successful for domain: ${domainName}, Response: ${JSON.stringify(response)}`,
                'CheckWebsiteBackup'
            );

            // Update is_request = true for that record
            await this.dataSource.manager.update(BackupWebsiteEntity, { id: backupRecord.id }, { is_request: true });

            this.loggerService.log(
                `CheckWebsiteBackup: Updated backup record ${backupRecord.id} - set is_request = true`,
                'CheckWebsiteBackup'
            );
        } catch (error) {
            this.loggerService.error(
                `CheckWebsiteBackup: Failed to process backup for domain ${domainName}: ${error.message}`,
                'CheckWebsiteBackup'
            );

            // Optionally, you might want to mark the record as failed or retry later
            // For now, we'll just log the error and continue with other records
        }
    }
}
