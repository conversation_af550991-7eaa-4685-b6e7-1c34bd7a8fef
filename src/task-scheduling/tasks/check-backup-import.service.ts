import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DataSource } from 'typeorm';
import { WebsiteStatus } from '../../entities/website.entity';
import { LoggerService } from '../../loggers/logger.service';
import { CronTemplateEntity } from '../../entities/cron-template.entity';
import { ReviewsieuxeAxios } from '../../axios/reviewsieuxe.axios';
import axios from 'axios';
import appConf from '../../configs/app.conf';

@Injectable()
export class CheckBackupImportService {
    constructor(
        private readonly dataSource: DataSource,
        private readonly loggerService: LoggerService,
        private readonly rsAxios: ReviewsieuxeAxios
    ) {}

    @Cron(CronExpression.EVERY_MINUTE)
    async handleCron() {
        const waitingBackupImport = await this.dataSource.manager
            .getRepository(CronTemplateEntity)
            .createQueryBuilder('cronTemplate')
            .where('cronTemplate.status_id = :statusId', { statusId: WebsiteStatus.INSTALLED })
            .andWhere('cronTemplate.job_id IS NOT NULL')
            .andWhere('cronTemplate.job_id != :emptyString', { emptyString: '' })
            .getMany();

        if (!waitingBackupImport.length) {
            this.loggerService.log('CheckBackupImport: No waiting backup import jobs');
            return;
        }

        for (let cronTemplateEntity of waitingBackupImport) {
            try {
                // Check import status
                const importStatusRes = await axios.get(
                    `https://${cronTemplateEntity.domain}/wp-json/template-toolkit/v1/import-status/${cronTemplateEntity.job_id}`,
                    {
                        headers: {
                            Authorization: `Bearer ${appConf.W_API_KEY}`,
                            'Content-Type': 'application/json',
                        },
                    }
                );

                const statusData = importStatusRes.data;

                if (statusData.status === 'downloaded' && statusData.file_info?.name) {
                    const restoreCommand = `ai1wm restore ${statusData.file_info.name} --yes`;

                    await this.rsAxios.wpcli(cronTemplateEntity.domain, restoreCommand);

                    // flush rewrite rules: settings > permalinks > Post name
                    await this.rsAxios.wpcli(cronTemplateEntity.domain, 'rewrite structure /%postname%/ --hard');

                    // disable designer-tagging plugin if exist
                    // const disableDesignerTagging = `plugin delete designer-tagging`;
                    const disableDesignerTagging = `plugin deactivate designer-tagging`;
                    await this.rsAxios.wpcli(cronTemplateEntity.domain, disableDesignerTagging);

                    // Install customer edit plugin
                    const installCustomerEdit = `plugin install '${appConf.API_URL}files/others/plugins/customer-edit.zip' --activate`;
                    await this.rsAxios.wpcli(cronTemplateEntity.domain, installCustomerEdit);

                    // flush rewrite rules: settings > permalinks > Post name
                    await this.rsAxios.wpcli(cronTemplateEntity.domain, 'rewrite structure /%postname%/ --hard');

                    await this.dataSource.manager.update(
                        CronTemplateEntity,
                        { id: cronTemplateEntity.id },
                        {
                            status_id: WebsiteStatus.ACTIVE,
                            job_id: '',
                        }
                    );

                    this.loggerService.log(
                        `CheckBackupImport: Successfully restored backup for ${cronTemplateEntity.domain}`
                    );
                } else {
                    this.loggerService.log(
                        `CheckBackupImport: Import still in progress for ${cronTemplateEntity.domain}, status: ${statusData.status}`
                    );
                }
            } catch (error) {
                this.loggerService.log(
                    `CheckBackupImport: Error checking import status for ${cronTemplateEntity.domain}: ${JSON.stringify(error)}`
                );
            }
        }
    }
}
