import { Modu<PERSON> } from '@nestjs/common';
import { SetExpiredTransactionsService } from './tasks/set-expired-transactions.service';
import { DeployWaitingCronTemplatesService } from './tasks/deploy-waiting-cron-templates.service';
import { CheckInstallingCronTemplatesService } from './tasks/check-installing-cron-templates.service';
import { CheckBackupImportService } from './tasks/check-backup-import.service';
import { CheckWebsiteBackupService } from './tasks/check-website-backup.service';
import { CheckWebsiteBackupDoneService } from './tasks/check-website-backup-done.service';

@Module({
    providers: [
        SetExpiredTransactionsService,
        DeployWaitingCronTemplatesService,
        CheckInstallingCronTemplatesService,
        CheckBackupImportService,
        CheckWebsiteBackupService,
        CheckWebsiteBackupDoneService,
    ],
})
export class TaskSchedulingModule {}
