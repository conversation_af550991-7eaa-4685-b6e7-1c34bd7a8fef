import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { BadRequestException, HttpStatus, ValidationPipe } from '@nestjs/common';
import helmet from 'helmet';
import * as compression from 'compression';
import appConf from './configs/app.conf';
import { useContainer } from 'class-validator';
import { HttpExceptionFilter } from './commons/filters/http-exception.filter';
import * as bodyParser from 'body-parser';
import * as timeout from 'connect-timeout';

async function bootstrap() {
    const app = await NestFactory.create(AppModule);
    app.useGlobalFilters(new HttpExceptionFilter());

    // ✅ Thêm middleware timeout (nếu file lớn upload chậm)
    app.use(timeout(`${appConf.REQUEST_TIMEOUT}m`)); //phút

    // ✅ Tăng giới hạn body (chỉ cần nếu upload kèm metadata JSON to)
    app.use(bodyParser.json({ limit: `${appConf.LIMIT_UPLOAD_SIZE + 1}gb` }));
    app.use(bodyParser.urlencoded({ limit: `${appConf.LIMIT_UPLOAD_SIZE + 1}gb`, extended: true }));

    // 🔒 Bảo vệ headers HTTP
    app.use(
        helmet({
            contentSecurityPolicy: appConf.NODE_ENV === 'production' ? undefined : false, // Tắt CSP khi ở dev
            crossOriginResourcePolicy: { policy: 'cross-origin' }, // Cho phép tải tài nguyên từ bên ngoài
        })
    );
    // 🌍 Kích hoạt CORS (Chỉ cho phép từ các domain cụ thể)
    app.enableCors({
        origin: appConf.CORS_ORIGIN, // Chỉ cho phép từ domain này
        methods: 'GET,POST', // 🔥 Chỉ cho phép các phương thức GraphQL cần
        //allowedHeaders: 'Content-Type,Authorization',
        allowedHeaders: 'Content-Type,Authorization,x-apollo-operation-name,apollo-require-preflight',
        credentials: true, // Cho phép cookie & header token
    });

    // 🚀 Nén dữ liệu
    app.use(compression());

    // ✅ Validation Pipes (Tự động kiểm tra dữ liệu đầu vào)
    app.useGlobalPipes(
        new ValidationPipe({
            whitelist: true, // Loại bỏ field không khai báo trong DTO
            forbidNonWhitelisted: false, // Chặn request có field lạ
            transform: true, // Tự động chuyển đổi kiểu dữ liệu
            exceptionFactory: (errors) => {
                const messages = errors.map((error) => ({
                    property: error.property,
                    constraints: error.constraints,
                }));
                return new BadRequestException({
                    statusCode: HttpStatus.BAD_REQUEST,
                    message: 'Validation failed',
                    errors: messages,
                });
            },
        })
    );

    //Đảm bảo rằng NestJS DI (Dependency Injection)
    useContainer(app.select(AppModule), { fallbackOnErrors: true });

    await app.listen(appConf.APP_PORT);

    // Xử lý khi NestJS bị tắt để tránh lỗi chiếm cổng
    process.on('SIGINT', async () => {
        console.log('🛑 Stopping server...');
        await app.close();
        process.exit(0);
    });

    process.on('SIGTERM', async () => {
        console.log('🛑 SIGTERM received. Closing server...');
        await app.close();
        process.exit(0);
    });
}
bootstrap();
