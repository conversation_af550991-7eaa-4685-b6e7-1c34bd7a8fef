import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePasswordResetsTable1744080027101 implements MigrationInterface {
    name = 'UpdatePasswordResetsTable1744080027101';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`TRUNCATE TABLE "password_resets" CASCADE`);
        await queryRunner.query(`ALTER TABLE "password_resets" ADD "otp" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "password_resets" ALTER COLUMN "token" SET NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "password_resets" ALTER COLUMN "token" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "password_resets" DROP COLUMN "otp"`);
    }
}
