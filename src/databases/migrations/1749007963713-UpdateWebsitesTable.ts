import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateWebsitesTable1749007963713 implements MigrationInterface {
    name = 'UpdateWebsitesTable1749007963713';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "websites" DROP CONSTRAINT "FK_902993674f7690d8c8bde62bdd6"`);
        await queryRunner.query(`ALTER TABLE "websites" RENAME COLUMN "subscription_id" TO "info"`);
        await queryRunner.query(`ALTER TABLE "websites" DROP COLUMN "info"`);
        await queryRunner.query(`ALTER TABLE "websites" ADD "info" jsonb`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "websites" DROP COLUMN "info"`);
        await queryRunner.query(`ALTER TABLE "websites" ADD "info" integer`);
        await queryRunner.query(`ALTER TABLE "websites" RENAME COLUMN "info" TO "subscription_id"`);
        await queryRunner.query(
            `ALTER TABLE "websites" ADD CONSTRAINT "FK_902993674f7690d8c8bde62bdd6" FOREIGN KEY ("subscription_id") REFERENCES "subscriptions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }
}
