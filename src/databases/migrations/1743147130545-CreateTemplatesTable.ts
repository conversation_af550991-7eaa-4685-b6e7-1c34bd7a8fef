import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTemplatesTable1743147130545 implements MigrationInterface {
    name = 'CreateTemplatesTable1743147130545';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "templates" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "desc" character varying, "status_id" smallint NOT NULL, "industry_id" integer NOT NULL, "designer_id" integer, "image_id" integer, "csv_file_id" integer, "code_file_id" integer, "info" jsonb, CONSTRAINT "REL_e09c23e75d1cc876faf4cbb2eb" UNIQUE ("industry_id"), CONSTRAINT "REL_1658fba9508c634003a6bc097a" UNIQUE ("designer_id"), CONSTRAINT "REL_1a54ed9ad9e60070556c542f00" UNIQUE ("image_id"), CONSTRAINT "REL_0a2fc20b8a103eb2e250fdae88" UNIQUE ("csv_file_id"), CONSTRAINT "REL_2ea222d63a066a6f4e87aa3580" UNIQUE ("code_file_id"), CONSTRAINT "PK_515948649ce0bbbe391de702ae5" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_e0753fa8d20f4eae08ebec1fef9" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_18fc25f0cfc9d0bbfc6fe04046c" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_df101c0b573e3d8f7fb07daaafa" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_e09c23e75d1cc876faf4cbb2eb9" FOREIGN KEY ("industry_id") REFERENCES "industries"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_1658fba9508c634003a6bc097ac" FOREIGN KEY ("designer_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_1a54ed9ad9e60070556c542f000" FOREIGN KEY ("image_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_0a2fc20b8a103eb2e250fdae881" FOREIGN KEY ("csv_file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_2ea222d63a066a6f4e87aa35807" FOREIGN KEY ("code_file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_2ea222d63a066a6f4e87aa35807"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_0a2fc20b8a103eb2e250fdae881"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_1a54ed9ad9e60070556c542f000"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_1658fba9508c634003a6bc097ac"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_e09c23e75d1cc876faf4cbb2eb9"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_df101c0b573e3d8f7fb07daaafa"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_18fc25f0cfc9d0bbfc6fe04046c"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_e0753fa8d20f4eae08ebec1fef9"`);
        await queryRunner.query(`DROP TABLE "templates"`);
    }
}
