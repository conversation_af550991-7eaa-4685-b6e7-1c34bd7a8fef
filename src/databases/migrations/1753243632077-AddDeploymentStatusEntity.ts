import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDeploymentStatusEntity1753243632077 implements MigrationInterface {
    name = 'AddDeploymentStatusEntity1753243632077';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "deployment_statuses" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "domain" character varying(255) NOT NULL, "step" character varying(100) NOT NULL, "success" boolean NOT NULL DEFAULT true, "extra" jsonb DEFAULT '{}', "timestamp" character varying(50) NOT NULL DEFAULT to_char(NOW(), 'YYYY-MM-DD"T"HH24:MI:SS"Z"'), CONSTRAINT "PK_29d4affbf2c073c4aef698f27ac" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(`CREATE INDEX "IDX_c4bc0d5dfbad8c982cc88c7d06" ON "deployment_statuses" ("domain") `);
        await queryRunner.query(
            `ALTER TABLE "deployment_statuses" ADD CONSTRAINT "FK_aa9b564623896cc5d6c7227bee4" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "deployment_statuses" ADD CONSTRAINT "FK_35bdb530cc840bcd24d95df5d5e" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "deployment_statuses" ADD CONSTRAINT "FK_9d113a2a70a88ebbf11978d407a" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "deployment_statuses" DROP CONSTRAINT "FK_9d113a2a70a88ebbf11978d407a"`);
        await queryRunner.query(`ALTER TABLE "deployment_statuses" DROP CONSTRAINT "FK_35bdb530cc840bcd24d95df5d5e"`);
        await queryRunner.query(`ALTER TABLE "deployment_statuses" DROP CONSTRAINT "FK_aa9b564623896cc5d6c7227bee4"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c4bc0d5dfbad8c982cc88c7d06"`);
        await queryRunner.query(`DROP TABLE "deployment_statuses"`);
    }
}
