import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTicketsTable1753416062398 implements MigrationInterface {
    name = 'CreateTicketsTable1753416062398';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "tickets" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "customer_id" integer NOT NULL, "website_id" integer, "title" character varying NOT NULL, "content" character varying, "type_id" smallint NOT NULL, "status_id" smallint NOT NULL, CONSTRAINT "PK_343bc942ae261cf7a1377f48fd0" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "tickets" ADD CONSTRAINT "FK_8798a589dc4c71b6d0e8c2b9fc3" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "tickets" ADD CONSTRAINT "FK_11e329dba935d03d626939e78ec" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "tickets" ADD CONSTRAINT "FK_9f2f29c58bf8b07a70c77bf4495" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "tickets" ADD CONSTRAINT "FK_42e4343476d9c4a46fb565a5c46" FOREIGN KEY ("customer_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "tickets" ADD CONSTRAINT "FK_1aa3811bc54240c29395fa102d6" FOREIGN KEY ("website_id") REFERENCES "websites"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tickets" DROP CONSTRAINT "FK_1aa3811bc54240c29395fa102d6"`);
        await queryRunner.query(`ALTER TABLE "tickets" DROP CONSTRAINT "FK_42e4343476d9c4a46fb565a5c46"`);
        await queryRunner.query(`ALTER TABLE "tickets" DROP CONSTRAINT "FK_9f2f29c58bf8b07a70c77bf4495"`);
        await queryRunner.query(`ALTER TABLE "tickets" DROP CONSTRAINT "FK_11e329dba935d03d626939e78ec"`);
        await queryRunner.query(`ALTER TABLE "tickets" DROP CONSTRAINT "FK_8798a589dc4c71b6d0e8c2b9fc3"`);
        await queryRunner.query(`DROP TABLE "tickets"`);
    }
}
