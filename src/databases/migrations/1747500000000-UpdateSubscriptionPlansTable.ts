import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateSubscriptionPlansTable1747500000000 implements MigrationInterface {
    name = 'UpdateSubscriptionPlansTable1747500000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "subscription_plans" ADD "original_price" numeric(8,2)`);
        await queryRunner.query(`ALTER TABLE "subscription_plans" ADD "sub_desc" character varying`);
        await queryRunner.query(`ALTER TABLE "subscription_plans" ADD "highlights" jsonb`);
        await queryRunner.query(`ALTER TABLE "subscription_plans" ADD "woo_subscription_id" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "subscription_plans" DROP COLUMN "woo_subscription_id"`);
        await queryRunner.query(`ALTER TABLE "subscription_plans" DROP COLUMN "highlights"`);
        await queryRunner.query(`ALTER TABLE "subscription_plans" DROP COLUMN "sub_desc"`);
        await queryRunner.query(`ALTER TABLE "subscription_plans" DROP COLUMN "original_price"`);
    }
}
