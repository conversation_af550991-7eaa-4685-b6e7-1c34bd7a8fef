import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTemplatesTable1743404682552 implements MigrationInterface {
    name = 'UpdateTemplatesTable1743404682552';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_e09c23e75d1cc876faf4cbb2eb9"`);
        await queryRunner.query(
            `CREATE TABLE "template_industries" ("template_id" integer NOT NULL, "industry_id" integer NOT NULL, CONSTRAINT "PK_1a4018e6bd2e7802484a9856e1f" PRIMARY KEY ("template_id", "industry_id"))`
        );
        await queryRunner.query(
            `CREATE INDEX "IDX_50df8cbc2a795a48ebfd594cde" ON "template_industries" ("template_id") `
        );
        await queryRunner.query(
            `CREATE INDEX "IDX_a1cf55ae3c2a98fa72741cbde4" ON "template_industries" ("industry_id") `
        );
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "REL_e09c23e75d1cc876faf4cbb2eb"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP COLUMN "industry_id"`);
        await queryRunner.query(
            `ALTER TABLE "template_industries" ADD CONSTRAINT "FK_50df8cbc2a795a48ebfd594cded" FOREIGN KEY ("template_id") REFERENCES "templates"("id") ON DELETE CASCADE ON UPDATE CASCADE`
        );
        await queryRunner.query(
            `ALTER TABLE "template_industries" ADD CONSTRAINT "FK_a1cf55ae3c2a98fa72741cbde4b" FOREIGN KEY ("industry_id") REFERENCES "industries"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "template_industries" DROP CONSTRAINT "FK_a1cf55ae3c2a98fa72741cbde4b"`);
        await queryRunner.query(`ALTER TABLE "template_industries" DROP CONSTRAINT "FK_50df8cbc2a795a48ebfd594cded"`);
        await queryRunner.query(`ALTER TABLE "templates" ADD "industry_id" integer NOT NULL`);
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "REL_e09c23e75d1cc876faf4cbb2eb" UNIQUE ("industry_id")`
        );
        await queryRunner.query(`DROP INDEX "public"."IDX_a1cf55ae3c2a98fa72741cbde4"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_50df8cbc2a795a48ebfd594cde"`);
        await queryRunner.query(`DROP TABLE "template_industries"`);
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_e09c23e75d1cc876faf4cbb2eb9" FOREIGN KEY ("industry_id") REFERENCES "industries"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }
}
