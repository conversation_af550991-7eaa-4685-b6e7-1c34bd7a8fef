import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateIndustriesTable1742541901401 implements MigrationInterface {
    name = 'CreateIndustriesTable1742541901401';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "industries" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "display_order" integer NOT NULL, "status_id" smallint NOT NULL, CONSTRAINT "PK_f1626dcb2d58142d7dfcca7b8d1" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(`ALTER TABLE "users" ADD "industry_id" integer`);
        await queryRunner.query(`ALTER TABLE "users" ADD "bio" character varying`);
        await queryRunner.query(
            `ALTER TABLE "industries" ADD CONSTRAINT "FK_d3060c33e6971de8b805545b7bf" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "industries" ADD CONSTRAINT "FK_4c93a27a9ba569a52066708fe28" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "industries" ADD CONSTRAINT "FK_507d4d7751f5a4451745f579db6" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "users" ADD CONSTRAINT "FK_3ac26f36b783abf6bbc18454819" FOREIGN KEY ("industry_id") REFERENCES "industries"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_3ac26f36b783abf6bbc18454819"`);
        await queryRunner.query(`ALTER TABLE "industries" DROP CONSTRAINT "FK_507d4d7751f5a4451745f579db6"`);
        await queryRunner.query(`ALTER TABLE "industries" DROP CONSTRAINT "FK_4c93a27a9ba569a52066708fe28"`);
        await queryRunner.query(`ALTER TABLE "industries" DROP CONSTRAINT "FK_d3060c33e6971de8b805545b7bf"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "bio"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "industry_id"`);
        await queryRunner.query(`DROP TABLE "industries"`);
    }
}
