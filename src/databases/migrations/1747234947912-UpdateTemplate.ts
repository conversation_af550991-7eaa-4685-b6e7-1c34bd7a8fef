import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTemplate1747234947912 implements MigrationInterface {
    name = 'UpdateTemplate1747234947912';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "templates" ADD "template_file_id" integer`);
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "UQ_3807ee6e6dbaf583f2ee9a1a5a2" UNIQUE ("template_file_id")`
        );
        await queryRunner.query(`ALTER TABLE "templates" ADD "sql_file_id" integer`);
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "UQ_2e076e06b049a142559d98bee39" UNIQUE ("sql_file_id")`
        );
        await queryRunner.query(`ALTER TABLE "templates" ALTER COLUMN "is_multiple" DROP DEFAULT`);
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_3807ee6e6dbaf583f2ee9a1a5a2" FOREIGN KEY ("template_file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_2e076e06b049a142559d98bee39" FOREIGN KEY ("sql_file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_2e076e06b049a142559d98bee39"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_3807ee6e6dbaf583f2ee9a1a5a2"`);
        await queryRunner.query(`ALTER TABLE "templates" ALTER COLUMN "is_multiple" SET DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "UQ_2e076e06b049a142559d98bee39"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP COLUMN "sql_file_id"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "UQ_3807ee6e6dbaf583f2ee9a1a5a2"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP COLUMN "template_file_id"`);
    }
}
