import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveUniqueConstraintFromSmtpCustomerId1753200222846 implements MigrationInterface {
    name = 'RemoveUniqueConstraintFromSmtpCustomerId1753200222846';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "smtps" DROP CONSTRAINT "UQ_6fbdb8ee16acc3574ed55eac33d"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "smtps" ADD CONSTRAINT "UQ_6fbdb8ee16acc3574ed55eac33d" UNIQUE ("customer_id")`
        );
    }
}
