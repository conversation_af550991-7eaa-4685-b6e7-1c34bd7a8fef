import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateBackupWebsitesTable1753266516863 implements MigrationInterface {
    name = 'CreateBackupWebsitesTable1753266516863';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "backup_websites" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "website_id" integer NOT NULL, "file_id" integer, CONSTRAINT "PK_868f521ae9d46903df5d0826afd" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "backup_websites" ADD CONSTRAINT "FK_297d906ac8ea925bea3e908b32a" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "backup_websites" ADD CONSTRAINT "FK_54ddab8270f7e68de5567305d97" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "backup_websites" ADD CONSTRAINT "FK_d806547a108ca58a91a20387432" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "backup_websites" ADD CONSTRAINT "FK_b8e69c11c563e3a6d957c1dc424" FOREIGN KEY ("website_id") REFERENCES "websites"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "backup_websites" ADD CONSTRAINT "FK_0f737a6f40bc7663509b40f0744" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "backup_websites" DROP CONSTRAINT "FK_0f737a6f40bc7663509b40f0744"`);
        await queryRunner.query(`ALTER TABLE "backup_websites" DROP CONSTRAINT "FK_b8e69c11c563e3a6d957c1dc424"`);
        await queryRunner.query(`ALTER TABLE "backup_websites" DROP CONSTRAINT "FK_d806547a108ca58a91a20387432"`);
        await queryRunner.query(`ALTER TABLE "backup_websites" DROP CONSTRAINT "FK_54ddab8270f7e68de5567305d97"`);
        await queryRunner.query(`ALTER TABLE "backup_websites" DROP CONSTRAINT "FK_297d906ac8ea925bea3e908b32a"`);
        await queryRunner.query(`DROP TABLE "backup_websites"`);
    }
}
