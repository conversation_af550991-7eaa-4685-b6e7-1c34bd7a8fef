import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPlansSub1749131313002 implements MigrationInterface {
    name = 'AddPlansSub1749131313002';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "transactions" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "customer_id" integer NOT NULL, "plan_id" integer, "type_id" smallint NOT NULL, "price" numeric(8,2) NOT NULL, "status_id" smallint NOT NULL, "start_date" date NOT NULL, "end_date" date NOT NULL, "website_id" integer, "info" jsonb NOT NULL, CONSTRAINT "PK_a219afd8dd77ed80f5a862f1db9" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "transactions" ADD CONSTRAINT "FK_77e84561125adeccf287547f66e" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "transactions" ADD CONSTRAINT "FK_d257215801b8676e1859a51884b" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "transactions" ADD CONSTRAINT "FK_0f313a19b178e032a2be12d0941" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "transactions" ADD CONSTRAINT "FK_6f09843c214f21a462b54b11e8d" FOREIGN KEY ("customer_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "transactions" ADD CONSTRAINT "FK_f742950c1d8977e09a23a3ccbc1" FOREIGN KEY ("website_id") REFERENCES "websites"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_f742950c1d8977e09a23a3ccbc1"`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_6f09843c214f21a462b54b11e8d"`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_0f313a19b178e032a2be12d0941"`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_d257215801b8676e1859a51884b"`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_77e84561125adeccf287547f66e"`);
        await queryRunner.query(`DROP TABLE "transactions"`);
    }
}
