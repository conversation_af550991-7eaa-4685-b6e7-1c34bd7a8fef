import { MigrationInterface, QueryRunner } from 'typeorm';

export class SeedConfigsTable1752565970560 implements MigrationInterface {
    name = 'SeedConfigsTable1752565970560';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "configs" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "code" character varying NOT NULL, "name" character varying NOT NULL, "value" text NOT NULL, CONSTRAINT "UQ_41a7f6abd76e3c8095ce0818d2a" UNIQUE ("code"), CONSTRAINT "PK_002b633ec0d45f5c6f928fea292" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "configs" ADD CONSTRAINT "FK_f1b4ee887ed7001a704c21e3ca4" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "configs" ADD CONSTRAINT "FK_3538c09ac4b50f566843153ff80" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "configs" ADD CONSTRAINT "FK_b9a353331129e2fe27b9684c2f4" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );

        await queryRunner.query(
            `INSERT INTO "configs" ("code", "name", "value", "created_at") VALUES
            ('admin_email', 'Administrator Email', '<EMAIL>', NOW()),
            ('site_maintenance', 'Site Maintenance Mode', 'false', NOW()),
            ('max_file_size', 'Maximum File Upload Size (MB)', '10', NOW()),
            ('backup_retention_days', 'Backup Retention Days', '30', NOW()),
            ('trial_period_days', 'Trial Period Days', '3', NOW()),
            ('max_wordpress_instances', 'Maximum WordPress Instances', '1000', NOW()),
            ('wordpress_instance_threshold', 'WordPress Instance Usage Threshold (%)', '75', NOW()),
            ('daily_backup_enabled', 'Daily Backup Enabled', 'true', NOW()),
            ('customer_backup_frequency', 'Customer Backup Frequency (days)', '3', NOW()),
            ('customer_backup_retention', 'Customer Backup Retention Count', '10', NOW())`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "configs" DROP CONSTRAINT "FK_b9a353331129e2fe27b9684c2f4"`);
        await queryRunner.query(`ALTER TABLE "configs" DROP CONSTRAINT "FK_3538c09ac4b50f566843153ff80"`);
        await queryRunner.query(`ALTER TABLE "configs" DROP CONSTRAINT "FK_f1b4ee887ed7001a704c21e3ca4"`);
        await queryRunner.query(`DROP TABLE "configs"`);
    }
}
