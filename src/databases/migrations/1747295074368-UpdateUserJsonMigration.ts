import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserJsonMigration1747295074368 implements MigrationInterface {
    name = 'UpdateUserJsonMigration1747295074368';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" ADD "info" jsonb`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "info"`);
    }
}
