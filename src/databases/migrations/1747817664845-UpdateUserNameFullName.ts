import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserNameFullName1747817664845 implements MigrationInterface {
    name = 'UpdateUserNameFullName1747817664845';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "users" ADD "full_name" character varying GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED NOT NULL`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "full_name"`);
    }
}
