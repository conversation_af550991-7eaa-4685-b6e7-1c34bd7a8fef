import { MigrationInterface, QueryRunner } from 'typeorm';

export class DataBaseTable1741151319046 implements MigrationInterface {
    name = 'DataBaseTable1741151319046';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "files" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "file_name" character varying NOT NULL, "file_url" character varying NOT NULL, "file_size" integer NOT NULL, "mime_type" character varying NOT NULL, CONSTRAINT "PK_6c16b9093a142e0e7613b04a3d9" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "actions" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "url" character varying, "icon" character varying, "parent_id" integer, "display_order" integer NOT NULL, CONSTRAINT "PK_7bfb822f56be449c0b8adbf83cf" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "groups" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, CONSTRAINT "PK_659d1483316afb28afd3a90646e" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "password_resets" ("id" SERIAL NOT NULL, "number_sent" integer NOT NULL, "user_id" integer NOT NULL, "token" character varying, "expires_at" TIMESTAMP NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, CONSTRAINT "PK_4816377aa98211c1de34469e742" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "personal_tokens" ("id" SERIAL NOT NULL, "user_id" integer NOT NULL, "access_token" character varying NOT NULL, "refresh_token" character varying NOT NULL, "is_enable" boolean NOT NULL, "expires_at" TIMESTAMP NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_278d2b70b7f8e7841366b3d5543" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "users" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "first_name" character varying NOT NULL, "last_name" character varying, "phone_number" character varying NOT NULL, "email" character varying NOT NULL, "password" character varying, "avatar_id" integer, "role_id" smallint NOT NULL, "status_id" smallint NOT NULL, "gender_id" smallint, "birthday" TIMESTAMP WITH TIME ZONE, "address" character varying, CONSTRAINT "REL_c3401836efedec3bec459c8f81" UNIQUE ("avatar_id"), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "group_actions" ("action_id" integer NOT NULL, "group_id" integer NOT NULL, CONSTRAINT "PK_8f32237aced16706a15801cca55" PRIMARY KEY ("action_id", "group_id"))`
        );
        await queryRunner.query(`CREATE INDEX "IDX_47e8f1b68800c433e21386d871" ON "group_actions" ("action_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_9867ea1c085165ce018a12474b" ON "group_actions" ("group_id") `);
        await queryRunner.query(
            `CREATE TABLE "user_groups" ("user_id" integer NOT NULL, "group_id" integer NOT NULL, CONSTRAINT "PK_c95039f66f5d7a452fc53945bfe" PRIMARY KEY ("user_id", "group_id"))`
        );
        await queryRunner.query(`CREATE INDEX "IDX_95bf94c61795df25a515435010" ON "user_groups" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_4c5f2c23c34f3921fbad2cd394" ON "user_groups" ("group_id") `);
        await queryRunner.query(
            `ALTER TABLE "files" ADD CONSTRAINT "FK_e92953fa5019c241b3f1a7c1520" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "files" ADD CONSTRAINT "FK_54a9f42e4ef8a919a32496ce0c6" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "files" ADD CONSTRAINT "FK_c21906fde5d27064246f271d5ee" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "actions" ADD CONSTRAINT "FK_87f47bca7c648f57c8b32d7eb8e" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "actions" ADD CONSTRAINT "FK_3e8dd3a26caaf623997eaf95c1e" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "actions" ADD CONSTRAINT "FK_23617c7539db791f3c9cf6ce645" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "actions" ADD CONSTRAINT "FK_87e021a95bb27363cea7ee0ca77" FOREIGN KEY ("parent_id") REFERENCES "actions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "groups" ADD CONSTRAINT "FK_a2fa29bfd5351b5b7ccacbc9f7c" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "groups" ADD CONSTRAINT "FK_6c8923b6933bb7c36f08e7870c0" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "groups" ADD CONSTRAINT "FK_ed1365ad8a7c673ccb918ee12a6" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "password_resets" ADD CONSTRAINT "FK_f7a4c3bc48f24df007936d217be" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "personal_tokens" ADD CONSTRAINT "FK_6830bc174c3bece0fb1d4aee28b" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "users" ADD CONSTRAINT "FK_f32b1cb14a9920477bcfd63df2c" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "users" ADD CONSTRAINT "FK_b75c92ef36f432fe68ec300a7d4" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "users" ADD CONSTRAINT "FK_021e2c9d9dca9f0885e8d738326" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "users" ADD CONSTRAINT "FK_c3401836efedec3bec459c8f818" FOREIGN KEY ("avatar_id") REFERENCES "files"("id") ON DELETE SET NULL ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "group_actions" ADD CONSTRAINT "FK_47e8f1b68800c433e21386d8713" FOREIGN KEY ("action_id") REFERENCES "actions"("id") ON DELETE CASCADE ON UPDATE CASCADE`
        );
        await queryRunner.query(
            `ALTER TABLE "group_actions" ADD CONSTRAINT "FK_9867ea1c085165ce018a12474b2" FOREIGN KEY ("group_id") REFERENCES "groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "user_groups" ADD CONSTRAINT "FK_95bf94c61795df25a5154350102" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE`
        );
        await queryRunner.query(
            `ALTER TABLE "user_groups" ADD CONSTRAINT "FK_4c5f2c23c34f3921fbad2cd3940" FOREIGN KEY ("group_id") REFERENCES "groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_groups" DROP CONSTRAINT "FK_4c5f2c23c34f3921fbad2cd3940"`);
        await queryRunner.query(`ALTER TABLE "user_groups" DROP CONSTRAINT "FK_95bf94c61795df25a5154350102"`);
        await queryRunner.query(`ALTER TABLE "group_actions" DROP CONSTRAINT "FK_9867ea1c085165ce018a12474b2"`);
        await queryRunner.query(`ALTER TABLE "group_actions" DROP CONSTRAINT "FK_47e8f1b68800c433e21386d8713"`);
        await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_c3401836efedec3bec459c8f818"`);
        await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_021e2c9d9dca9f0885e8d738326"`);
        await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_b75c92ef36f432fe68ec300a7d4"`);
        await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_f32b1cb14a9920477bcfd63df2c"`);
        await queryRunner.query(`ALTER TABLE "personal_tokens" DROP CONSTRAINT "FK_6830bc174c3bece0fb1d4aee28b"`);
        await queryRunner.query(`ALTER TABLE "password_resets" DROP CONSTRAINT "FK_f7a4c3bc48f24df007936d217be"`);
        await queryRunner.query(`ALTER TABLE "groups" DROP CONSTRAINT "FK_ed1365ad8a7c673ccb918ee12a6"`);
        await queryRunner.query(`ALTER TABLE "groups" DROP CONSTRAINT "FK_6c8923b6933bb7c36f08e7870c0"`);
        await queryRunner.query(`ALTER TABLE "groups" DROP CONSTRAINT "FK_a2fa29bfd5351b5b7ccacbc9f7c"`);
        await queryRunner.query(`ALTER TABLE "actions" DROP CONSTRAINT "FK_87e021a95bb27363cea7ee0ca77"`);
        await queryRunner.query(`ALTER TABLE "actions" DROP CONSTRAINT "FK_23617c7539db791f3c9cf6ce645"`);
        await queryRunner.query(`ALTER TABLE "actions" DROP CONSTRAINT "FK_3e8dd3a26caaf623997eaf95c1e"`);
        await queryRunner.query(`ALTER TABLE "actions" DROP CONSTRAINT "FK_87f47bca7c648f57c8b32d7eb8e"`);
        await queryRunner.query(`ALTER TABLE "files" DROP CONSTRAINT "FK_c21906fde5d27064246f271d5ee"`);
        await queryRunner.query(`ALTER TABLE "files" DROP CONSTRAINT "FK_54a9f42e4ef8a919a32496ce0c6"`);
        await queryRunner.query(`ALTER TABLE "files" DROP CONSTRAINT "FK_e92953fa5019c241b3f1a7c1520"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4c5f2c23c34f3921fbad2cd394"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_95bf94c61795df25a515435010"`);
        await queryRunner.query(`DROP TABLE "user_groups"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9867ea1c085165ce018a12474b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_47e8f1b68800c433e21386d871"`);
        await queryRunner.query(`DROP TABLE "group_actions"`);
        await queryRunner.query(`DROP TABLE "users"`);
        await queryRunner.query(`DROP TABLE "personal_tokens"`);
        await queryRunner.query(`DROP TABLE "password_resets"`);
        await queryRunner.query(`DROP TABLE "groups"`);
        await queryRunner.query(`DROP TABLE "actions"`);
        await queryRunner.query(`DROP TABLE "files"`);
    }
}
