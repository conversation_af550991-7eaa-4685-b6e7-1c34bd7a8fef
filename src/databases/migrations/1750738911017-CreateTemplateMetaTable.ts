import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTemplateMetaTable1750738911017 implements MigrationInterface {
    name = 'CreateTemplateMetaTable1750738911017';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "template_meta" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "template_id" integer NOT NULL, "user_id" integer NOT NULL, "domain" character varying NOT NULL, "page_slug" character varying NOT NULL, "meta_key" character varying NOT NULL, "meta_value" character varying NOT NULL, CONSTRAINT "PK_fcbd15b9b2844ee4cca760c22d1" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "template_meta" ADD CONSTRAINT "FK_fc31d8422ad6888bf8a2e7aaf6b" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "template_meta" ADD CONSTRAINT "FK_a1bbd6a6e664b4d136c42958fbd" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "template_meta" ADD CONSTRAINT "FK_7d9cf85c739a78af3ca0d968673" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "template_meta" ADD CONSTRAINT "FK_245cfda338e4ce14f3f24c86b8f" FOREIGN KEY ("template_id") REFERENCES "templates"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "template_meta" ADD CONSTRAINT "FK_31ed1bb47b9f3774178cc90513c" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "template_meta" DROP CONSTRAINT "FK_31ed1bb47b9f3774178cc90513c"`);
        await queryRunner.query(`ALTER TABLE "template_meta" DROP CONSTRAINT "FK_245cfda338e4ce14f3f24c86b8f"`);
        await queryRunner.query(`ALTER TABLE "template_meta" DROP CONSTRAINT "FK_7d9cf85c739a78af3ca0d968673"`);
        await queryRunner.query(`ALTER TABLE "template_meta" DROP CONSTRAINT "FK_a1bbd6a6e664b4d136c42958fbd"`);
        await queryRunner.query(`ALTER TABLE "template_meta" DROP CONSTRAINT "FK_fc31d8422ad6888bf8a2e7aaf6b"`);
        await queryRunner.query(`DROP TABLE "template_meta"`);
    }
}
