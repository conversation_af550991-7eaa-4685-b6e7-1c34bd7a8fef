import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsProcessRestoreToBackupWebsites1753328981087 implements MigrationInterface {
    name = 'AddIsProcessRestoreToBackupWebsites1753328981087';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "backup_websites" ADD "is_process_restore" boolean NOT NULL DEFAULT false`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "backup_websites" DROP COLUMN "is_process_restore"`);
    }
}
