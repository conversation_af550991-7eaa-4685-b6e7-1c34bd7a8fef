import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateSubscriptionsTable1743502513128 implements MigrationInterface {
    name = 'CreateSubscriptionsTable1743502513128';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "subscriptions" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "customer_id" integer NOT NULL, "plan_id" integer NOT NULL, "price" numeric(8,2) NOT NULL, "features" jsonb NOT NULL, "status_id" smallint NOT NULL, "payment_method" character varying NOT NULL, "start_date" TIMESTAMP WITH TIME ZONE NOT NULL, "end_date" TIMESTAMP WITH TIME ZONE NOT NULL, CONSTRAINT "REL_98a4e1e3025f768de1493ecede" UNIQUE ("customer_id"), CONSTRAINT "REL_e45fca5d912c3a2fab512ac25d" UNIQUE ("plan_id"), CONSTRAINT "PK_a87248d73155605cf782be9ee5e" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "subscriptions" ADD CONSTRAINT "FK_9a92203195f8367519fd64dbe09" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "subscriptions" ADD CONSTRAINT "FK_e8b5d97abb5a91550b668a965d4" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "subscriptions" ADD CONSTRAINT "FK_611360d97039ddc16cf2b9a7b92" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "subscriptions" ADD CONSTRAINT "FK_98a4e1e3025f768de1493ecedec" FOREIGN KEY ("customer_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "subscriptions" ADD CONSTRAINT "FK_e45fca5d912c3a2fab512ac25dc" FOREIGN KEY ("plan_id") REFERENCES "subscription_plans"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_e45fca5d912c3a2fab512ac25dc"`);
        await queryRunner.query(`ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_98a4e1e3025f768de1493ecedec"`);
        await queryRunner.query(`ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_611360d97039ddc16cf2b9a7b92"`);
        await queryRunner.query(`ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_e8b5d97abb5a91550b668a965d4"`);
        await queryRunner.query(`ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_9a92203195f8367519fd64dbe09"`);
        await queryRunner.query(`DROP TABLE "subscriptions"`);
    }
}
