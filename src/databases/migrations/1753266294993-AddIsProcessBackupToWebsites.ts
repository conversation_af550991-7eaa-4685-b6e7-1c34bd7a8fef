import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsProcessBackupToWebsites1753266294993 implements MigrationInterface {
    name = 'AddIsProcessBackupToWebsites1753266294993';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "websites" ADD "is_process_backup" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "websites" DROP COLUMN "is_process_backup"`);
    }
}
