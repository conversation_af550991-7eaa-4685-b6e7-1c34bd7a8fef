import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTemplateTable1747213641871 implements MigrationInterface {
    name = 'UpdateTemplateTable1747213641871';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "templates" ADD "is_multiple" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "templates" ADD "approved_date" TIMESTAMP`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "templates" DROP COLUMN "approved_date"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP COLUMN "is_multiple"`);
    }
}
