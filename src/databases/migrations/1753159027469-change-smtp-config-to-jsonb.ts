import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeSmtpConfigToJsonb1753159027469 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // First, update any null values to empty JSON object
        await queryRunner.query(`UPDATE "smtps" SET "smtp_config" = '{}' WHERE "smtp_config" IS NULL`);

        // Convert existing text values to JSON format if they're not already JSON
        await queryRunner.query(`
            UPDATE "smtps"
            SET "smtp_config" = CASE
                WHEN "smtp_config" ~ '^\\s*[{\\[]' THEN "smtp_config"
                ELSE '"' || "smtp_config" || '"'
            END
            WHERE "smtp_config" IS NOT NULL
        `);

        // Add a temporary column with jsonb type
        await queryRunner.query(`ALTER TABLE "smtps" ADD COLUMN "smtp_config_temp" jsonb`);

        // Copy data from text column to jsonb column
        await queryRunner.query(`UPDATE "smtps" SET "smtp_config_temp" = "smtp_config"::jsonb`);

        // Drop the old text column
        await queryRunner.query(`ALTER TABLE "smtps" DROP COLUMN "smtp_config"`);

        // Rename the temp column to the original name
        await queryRunner.query(`ALTER TABLE "smtps" RENAME COLUMN "smtp_config_temp" TO "smtp_config"`);

        // Add NOT NULL constraint
        await queryRunner.query(`ALTER TABLE "smtps" ALTER COLUMN "smtp_config" SET NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Add a temporary text column
        await queryRunner.query(`ALTER TABLE "smtps" ADD COLUMN "smtp_config_temp" text`);

        // Convert jsonb data back to text
        await queryRunner.query(`UPDATE "smtps" SET "smtp_config_temp" = "smtp_config"::text`);

        // Drop the jsonb column
        await queryRunner.query(`ALTER TABLE "smtps" DROP COLUMN "smtp_config"`);

        // Rename the temp column to the original name
        await queryRunner.query(`ALTER TABLE "smtps" RENAME COLUMN "smtp_config_temp" TO "smtp_config"`);

        // Add NOT NULL constraint
        await queryRunner.query(`ALTER TABLE "smtps" ALTER COLUMN "smtp_config" SET NOT NULL`);
    }
}
