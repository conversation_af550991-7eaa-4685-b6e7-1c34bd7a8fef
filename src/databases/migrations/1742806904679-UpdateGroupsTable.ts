import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateGroupsTable1742806904679 implements MigrationInterface {
    name = 'UpdateGroupsTable1742806904679';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "groups" ADD "desc" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "groups" DROP COLUMN "desc"`);
    }
}
