import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCronTemplate1750784490835 implements MigrationInterface {
    name = 'UpdateCronTemplate1750784490835';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "cron_templates" DROP CONSTRAINT "FK_7d4d8f1929c6664ecb691c2fb0c"`);
        await queryRunner.query(`ALTER TABLE "cron_templates" ALTER COLUMN "template_id" DROP NOT NULL`);
        await queryRunner.query(
            `ALTER TABLE "cron_templates" ADD CONSTRAINT "FK_7d4d8f1929c6664ecb691c2fb0c" FOREIGN KEY ("template_id") REFERENCES "templates"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "cron_templates" DROP CONSTRAINT "FK_7d4d8f1929c6664ecb691c2fb0c"`);
        await queryRunner.query(`ALTER TABLE "cron_templates" ALTER COLUMN "template_id" SET NOT NULL`);
        await queryRunner.query(
            `ALTER TABLE "cron_templates" ADD CONSTRAINT "FK_7d4d8f1929c6664ecb691c2fb0c" FOREIGN KEY ("template_id") REFERENCES "templates"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }
}
