import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTemplatesTable1751337517908 implements MigrationInterface {
    name = 'UpdateTemplatesTable1751337517908';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "templates" ADD "reject_reason" character varying`);
        await queryRunner.query(`ALTER TABLE "templates" ADD "reject_file_id" integer`);
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "UQ_4ba569365c9b7fca23b400e45ee" UNIQUE ("reject_file_id")`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_4ba569365c9b7fca23b400e45ee" FOREIGN KEY ("reject_file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_4ba569365c9b7fca23b400e45ee"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "UQ_4ba569365c9b7fca23b400e45ee"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP COLUMN "reject_file_id"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP COLUMN "reject_reason"`);
    }
}
