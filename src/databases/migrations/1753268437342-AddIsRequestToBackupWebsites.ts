import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsRequestToBackupWebsites1753268437342 implements MigrationInterface {
    name = 'AddIsRequestToBackupWebsites1753268437342';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "backup_websites" ADD "is_request" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "backup_websites" DROP COLUMN "is_request"`);
    }
}
