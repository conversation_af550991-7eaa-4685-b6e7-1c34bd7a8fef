import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTemplatesTable1749201511152 implements MigrationInterface {
    name = 'UpdateTemplatesTable1749201511152';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "templates" ADD "is_kit" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "templates" DROP COLUMN "is_kit"`);
    }
}
