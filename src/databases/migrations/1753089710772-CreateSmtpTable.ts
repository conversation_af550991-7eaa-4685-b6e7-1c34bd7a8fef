import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateSmtpTable1753089710772 implements MigrationInterface {
    name = 'CreateSmtpTable1753089710772';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "smtps" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "customer_id" integer NOT NULL, "smtp_config" text NOT NULL, CONSTRAINT "UQ_6fbdb8ee16acc3574ed55eac33d" UNIQUE ("customer_id"), CONSTRAINT "PK_559a7c0d1c87dd50640b3e75c33" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "smtps" ADD CONSTRAINT "FK_29a82accbb8d94ad51aca087485" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "smtps" ADD CONSTRAINT "FK_ba750385507668035def8d877d9" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "smtps" ADD CONSTRAINT "FK_dc66f00e6b07de2c87f45f97d70" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "smtps" DROP CONSTRAINT "FK_dc66f00e6b07de2c87f45f97d70"`);
        await queryRunner.query(`ALTER TABLE "smtps" DROP CONSTRAINT "FK_ba750385507668035def8d877d9"`);
        await queryRunner.query(`ALTER TABLE "smtps" DROP CONSTRAINT "FK_29a82accbb8d94ad51aca087485"`);
        await queryRunner.query(`DROP TABLE "smtps"`);
    }
}
