import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTableWebsite1748250649004 implements MigrationInterface {
    name = 'AddTableWebsite1748250649004';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "websites" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "customer_id" integer NOT NULL, "template_id" integer, "domain" character varying, "subscription_id" integer, CONSTRAINT "PK_da80e50ae0d986d43c9eb80ab70" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "websites" ADD CONSTRAINT "FK_4470ed544160ca7f543803a4bbc" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "websites" ADD CONSTRAINT "FK_02f7427f5751af17dbab8b6aede" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "websites" ADD CONSTRAINT "FK_496a7cb1c20d696449b756ab2e0" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "websites" ADD CONSTRAINT "FK_7e7f43a2526858217c8998e2230" FOREIGN KEY ("customer_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "websites" ADD CONSTRAINT "FK_d3ab0175e7567054b29e62aaeee" FOREIGN KEY ("template_id") REFERENCES "templates"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "websites" ADD CONSTRAINT "FK_902993674f7690d8c8bde62bdd6" FOREIGN KEY ("subscription_id") REFERENCES "subscriptions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "websites" DROP CONSTRAINT "FK_902993674f7690d8c8bde62bdd6"`);
        await queryRunner.query(`ALTER TABLE "websites" DROP CONSTRAINT "FK_d3ab0175e7567054b29e62aaeee"`);
        await queryRunner.query(`ALTER TABLE "websites" DROP CONSTRAINT "FK_7e7f43a2526858217c8998e2230"`);
        await queryRunner.query(`ALTER TABLE "websites" DROP CONSTRAINT "FK_496a7cb1c20d696449b756ab2e0"`);
        await queryRunner.query(`ALTER TABLE "websites" DROP CONSTRAINT "FK_02f7427f5751af17dbab8b6aede"`);
        await queryRunner.query(`ALTER TABLE "websites" DROP CONSTRAINT "FK_4470ed544160ca7f543803a4bbc"`);
        await queryRunner.query(`DROP TABLE "websites"`);
    }
}
