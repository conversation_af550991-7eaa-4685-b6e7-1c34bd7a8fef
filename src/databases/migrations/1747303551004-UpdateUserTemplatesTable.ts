import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserTemplatesTable1747303551004 implements MigrationInterface {
    name = 'UpdateUserTemplatesTable1747303551004';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "user_templates" ("template_id" integer NOT NULL, "user_id" integer NOT NULL, CONSTRAINT "PK_89becf35108624687378001e60c" PRIMARY KEY ("template_id", "user_id"))`
        );
        await queryRunner.query(`CREATE INDEX "IDX_fbb48a475ada996b44aee59fdb" ON "user_templates" ("template_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_c3a93dda3f2c7f2f71d3b424cf" ON "user_templates" ("user_id") `);
        await queryRunner.query(
            `ALTER TABLE "user_templates" ADD CONSTRAINT "FK_fbb48a475ada996b44aee59fdb1" FOREIGN KEY ("template_id") REFERENCES "templates"("id") ON DELETE CASCADE ON UPDATE CASCADE`
        );
        await queryRunner.query(
            `ALTER TABLE "user_templates" ADD CONSTRAINT "FK_c3a93dda3f2c7f2f71d3b424cfe" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_templates" DROP CONSTRAINT "FK_c3a93dda3f2c7f2f71d3b424cfe"`);
        await queryRunner.query(`ALTER TABLE "user_templates" DROP CONSTRAINT "FK_fbb48a475ada996b44aee59fdb1"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c3a93dda3f2c7f2f71d3b424cf"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_fbb48a475ada996b44aee59fdb"`);
        await queryRunner.query(`DROP TABLE "user_templates"`);
    }
}
