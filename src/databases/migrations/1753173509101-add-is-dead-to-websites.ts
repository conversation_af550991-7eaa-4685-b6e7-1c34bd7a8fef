import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsDeadToWebsites1753173509101 implements MigrationInterface {
    name = 'AddIsDeadToWebsites1753173509101';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "websites" ADD "is_dead" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "websites" DROP COLUMN "is_dead"`);
    }
}
