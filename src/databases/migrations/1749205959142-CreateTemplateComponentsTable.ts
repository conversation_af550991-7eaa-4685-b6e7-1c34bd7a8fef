import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTemplateComponentsTable1749205959142 implements MigrationInterface {
    name = 'CreateTemplateComponentsTable1749205959142';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "template_components" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "desc" character varying, "status_id" smallint NOT NULL, "file_id" integer, CONSTRAINT "PK_032bcfc944d39745250d3d0fd89" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "template_components" ADD CONSTRAINT "FK_f9764867959880488d6fbe08c78" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "template_components" ADD CONSTRAINT "FK_98d0108d2d875eb4d18acb38925" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "template_components" ADD CONSTRAINT "FK_f20074b4d393cca0956b8bf5251" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "template_components" ADD CONSTRAINT "FK_239202e40501deb8b6c7c7beda0" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE SET NULL ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "template_components" DROP CONSTRAINT "FK_239202e40501deb8b6c7c7beda0"`);
        await queryRunner.query(`ALTER TABLE "template_components" DROP CONSTRAINT "FK_f20074b4d393cca0956b8bf5251"`);
        await queryRunner.query(`ALTER TABLE "template_components" DROP CONSTRAINT "FK_98d0108d2d875eb4d18acb38925"`);
        await queryRunner.query(`ALTER TABLE "template_components" DROP CONSTRAINT "FK_f9764867959880488d6fbe08c78"`);
        await queryRunner.query(`DROP TABLE "template_components"`);
    }
}
