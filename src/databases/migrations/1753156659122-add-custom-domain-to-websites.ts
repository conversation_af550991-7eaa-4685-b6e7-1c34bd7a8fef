import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCustomDomainToWebsites1753156659122 implements MigrationInterface {
    name = 'AddCustomDomainToWebsites1753156659122';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "websites" ADD "custom_domain" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "websites" DROP COLUMN "custom_domain"`);
    }
}
