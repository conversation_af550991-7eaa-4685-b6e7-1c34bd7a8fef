import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateSubscriptionPlansTable1743490158129 implements MigrationInterface {
    name = 'CreateSubscriptionPlansTable1743490158129';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "subscription_plans" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "desc" character varying, "status_id" smallint NOT NULL, "price" numeric(8,2) NOT NULL, "display_order" integer NOT NULL, "features" jsonb NOT NULL, CONSTRAINT "PK_9ab8fe6918451ab3d0a4fb6bb0c" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "subscription_plans" ADD CONSTRAINT "FK_d27e0aceeab0c26b7b07256af4e" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "subscription_plans" ADD CONSTRAINT "FK_fa841a4e903bacded89eecc6f08" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "subscription_plans" ADD CONSTRAINT "FK_6cbbd58ccfff7fa82680e7317a0" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "subscription_plans" DROP CONSTRAINT "FK_6cbbd58ccfff7fa82680e7317a0"`);
        await queryRunner.query(`ALTER TABLE "subscription_plans" DROP CONSTRAINT "FK_fa841a4e903bacded89eecc6f08"`);
        await queryRunner.query(`ALTER TABLE "subscription_plans" DROP CONSTRAINT "FK_d27e0aceeab0c26b7b07256af4e"`);
        await queryRunner.query(`DROP TABLE "subscription_plans"`);
    }
}
