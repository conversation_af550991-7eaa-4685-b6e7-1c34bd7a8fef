import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateWebsitesTable1749180906679 implements MigrationInterface {
    name = 'UpdateWebsitesTable1749180906679';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "websites" ADD "status_id" smallint NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "websites" DROP COLUMN "status_id"`);
    }
}
