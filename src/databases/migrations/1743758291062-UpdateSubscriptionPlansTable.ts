import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateSubscriptionPlansTable1743758291062 implements MigrationInterface {
    name = 'UpdateSubscriptionPlansTable1743758291062';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "subscription_plans" ADD "type_id" smallint NOT NULL DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE "subscription_plans" ADD "trial_days" smallint`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "subscription_plans" DROP COLUMN "trial_days"`);
        await queryRunner.query(`ALTER TABLE "subscription_plans" DROP COLUMN "type_id"`);
    }
}
