import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTemplatesTable1746584179203 implements MigrationInterface {
    name = 'UpdateTemplatesTable1746584179203';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_1658fba9508c634003a6bc097ac"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "REL_1658fba9508c634003a6bc097a"`);
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_1658fba9508c634003a6bc097ac" FOREIGN KEY ("designer_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_1658fba9508c634003a6bc097ac"`);
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "REL_1658fba9508c634003a6bc097a" UNIQUE ("designer_id")`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_1658fba9508c634003a6bc097ac" FOREIGN KEY ("designer_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }
}
