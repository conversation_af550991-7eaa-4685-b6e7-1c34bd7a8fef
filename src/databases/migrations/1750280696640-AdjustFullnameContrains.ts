import { MigrationInterface, QueryRunner } from 'typeorm';

export class AdjustFullnameContrains1750280696640 implements MigrationInterface {
    name = 'AdjustFullnameContrains1750280696640';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "full_name"`);
        await queryRunner.query(
            `DELETE FROM "typeorm_metadata" WHERE "type" = $1 AND "name" = $2 AND "database" = $3 AND "schema" = $4 AND "table" = $5`,
            ['GENERATED_COLUMN', 'full_name', 'ipt', 'public', 'users']
        );
        await queryRunner.query(
            `ALTER TABLE "users" ADD "full_name" character varying GENERATED ALWAYS AS (first_name || ' ' || COALESCE(last_name, '')) STORED NOT NULL`
        );
        await queryRunner.query(
            `INSERT INTO "typeorm_metadata"("database", "schema", "table", "type", "name", "value") VALUES ($1, $2, $3, $4, $5, $6)`,
            ['ipt', 'public', 'users', 'GENERATED_COLUMN', 'full_name', "first_name || ' ' || COALESCE(last_name, '')"]
        );
        await queryRunner.query(`ALTER TABLE "subscription_plans" ALTER COLUMN "features" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "subscription_plans" ALTER COLUMN "features" SET NOT NULL`);
        await queryRunner.query(
            `DELETE FROM "typeorm_metadata" WHERE "type" = $1 AND "name" = $2 AND "database" = $3 AND "schema" = $4 AND "table" = $5`,
            ['GENERATED_COLUMN', 'full_name', 'ipt', 'public', 'users']
        );
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "full_name"`);
        await queryRunner.query(
            `INSERT INTO "typeorm_metadata"("database", "schema", "table", "type", "name", "value") VALUES ($1, $2, $3, $4, $5, $6)`,
            ['ipt', 'public', 'users', 'GENERATED_COLUMN', 'full_name', "first_name || ' ' || last_name"]
        );
        await queryRunner.query(
            `ALTER TABLE "users" ADD "full_name" character varying GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED NOT NULL`
        );
    }
}
