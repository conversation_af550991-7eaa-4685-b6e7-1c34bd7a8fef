import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFeedbackToTickets1753417467784 implements MigrationInterface {
    name = 'AddFeedbackToTickets1753417467784';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tickets" ADD "feedback" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tickets" DROP COLUMN "feedback"`);
    }
}
