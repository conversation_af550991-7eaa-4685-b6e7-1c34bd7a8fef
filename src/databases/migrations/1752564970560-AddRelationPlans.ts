import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRelationPlans1752564970560 implements MigrationInterface {
    name = 'AddRelationPlans1752564970560';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "transactions" ADD CONSTRAINT "FK_34e1bfd6bba9e1c1983a236aca3" FOREIGN KEY ("plan_id") REFERENCES "subscription_plans"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_34e1bfd6bba9e1c1983a236aca3"`);
    }
}
