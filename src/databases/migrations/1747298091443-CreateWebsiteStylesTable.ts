import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateWebsiteStylesTable1747298091443 implements MigrationInterface {
    name = 'CreateWebsiteStylesTable1747298091443';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "website_styles" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "desc" character varying, "display_order" integer NOT NULL, "status_id" smallint NOT NULL, CONSTRAINT "PK_dd0e84a4a067f92734ff937f042" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "website_styles" ADD CONSTRAINT "FK_bdc1a5d72ab2ec2e2ea5a0d33f4" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "website_styles" ADD CONSTRAINT "FK_281639e14cfa7954fe0dce62fec" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "website_styles" ADD CONSTRAINT "FK_3ff36889d49e476a32df07c43f2" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "website_styles" DROP CONSTRAINT "FK_3ff36889d49e476a32df07c43f2"`);
        await queryRunner.query(`ALTER TABLE "website_styles" DROP CONSTRAINT "FK_281639e14cfa7954fe0dce62fec"`);
        await queryRunner.query(`ALTER TABLE "website_styles" DROP CONSTRAINT "FK_bdc1a5d72ab2ec2e2ea5a0d33f4"`);
        await queryRunner.query(`DROP TABLE "website_styles"`);
    }
}
