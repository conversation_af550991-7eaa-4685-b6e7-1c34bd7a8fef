import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateMasterTagsTable1750301794146 implements MigrationInterface {
    name = 'CreateMasterTagsTable1750301794146';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "master_tags" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "type" character varying NOT NULL, "display_name" character varying NOT NULL, "tool_tip" character varying, CONSTRAINT "UQ_8136d2d3cd0b91e199e5699c002" UNIQUE ("name"), CONSTRAINT "PK_b9ce161a849b85f14f972ffec2b" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "master_tags" ADD CONSTRAINT "FK_b02ce92a71008b742cebb2370a7" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "master_tags" ADD CONSTRAINT "FK_f4ffaac05d4783a318f4097530a" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "master_tags" ADD CONSTRAINT "FK_b06cd2cc7afe0e6fe6a1ae64bb0" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "master_tags" DROP CONSTRAINT "FK_b06cd2cc7afe0e6fe6a1ae64bb0"`);
        await queryRunner.query(`ALTER TABLE "master_tags" DROP CONSTRAINT "FK_f4ffaac05d4783a318f4097530a"`);
        await queryRunner.query(`ALTER TABLE "master_tags" DROP CONSTRAINT "FK_b02ce92a71008b742cebb2370a7"`);
        await queryRunner.query(`DROP TABLE "master_tags"`);
    }
}
