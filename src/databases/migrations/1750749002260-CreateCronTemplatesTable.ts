import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCronTemplatesTable1750749002260 implements MigrationInterface {
    name = 'CreateCronTemplatesTable1750749002260';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "cron_templates" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "template_id" integer NOT NULL, "domain" character varying NOT NULL, "status_id" smallint NOT NULL, "deploy_status" jsonb, CONSTRAINT "UQ_33d4e0d3744d2d4021bc8d3e4d4" UNIQUE ("domain"), CONSTRAINT "PK_b1671e3f6f30cac086a52fdb3be" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "cron_templates" ADD CONSTRAINT "FK_9980e30e68cf164782889f85bab" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "cron_templates" ADD CONSTRAINT "FK_26065d42d5b801e36f97841cbc5" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "cron_templates" ADD CONSTRAINT "FK_7b3c2669067162fbe55b6075eb2" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "cron_templates" ADD CONSTRAINT "FK_7d4d8f1929c6664ecb691c2fb0c" FOREIGN KEY ("template_id") REFERENCES "templates"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "cron_templates" DROP CONSTRAINT "FK_7d4d8f1929c6664ecb691c2fb0c"`);
        await queryRunner.query(`ALTER TABLE "cron_templates" DROP CONSTRAINT "FK_7b3c2669067162fbe55b6075eb2"`);
        await queryRunner.query(`ALTER TABLE "cron_templates" DROP CONSTRAINT "FK_26065d42d5b801e36f97841cbc5"`);
        await queryRunner.query(`ALTER TABLE "cron_templates" DROP CONSTRAINT "FK_9980e30e68cf164782889f85bab"`);
        await queryRunner.query(`DROP TABLE "cron_templates"`);
    }
}
