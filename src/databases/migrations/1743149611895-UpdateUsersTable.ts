import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUsersTable1743149611895 implements MigrationInterface {
    name = 'UpdateUsersTable1743149611895';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "phone_number" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "phone_number" SET NOT NULL`);
    }
}
