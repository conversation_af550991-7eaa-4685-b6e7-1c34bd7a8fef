import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUsersTable1741751292486 implements MigrationInterface {
    name = 'UpdateUsersTable1741751292486';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" ADD "uid" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "uid"`);
    }
}
