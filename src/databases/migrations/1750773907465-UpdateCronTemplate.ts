import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCronTemplate1750773907465 implements MigrationInterface {
    name = 'UpdateCronTemplate1750773907465';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "cron_templates" ADD "job_id" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "cron_templates" DROP COLUMN "job_id"`);
    }
}
