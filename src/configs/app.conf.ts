import * as dotenv from 'dotenv';
dotenv.config();
dotenv.config({ path: '.env' });

export const appConf = {
    NODE_ENV: process.env.NODE_ENV || 'development',
    PG_HOST: process.env.PG_HOST || 'localhost',
    PG_PORT: parseInt(process.env.PG_PORT || '5432'),
    PG_USER: process.env.PG_USER || 'postgres',
    PG_PASS: process.env.PG_PASS || '',
    PG_DB: process.env.PG_DB || 'test',
    CORS_ORIGIN: process.env.CORS_ORIGIN || '*',
    APP_PORT: parseInt(process.env.APP_PORT || '8008'),
    AT_SECRET: process.env.AT_SECRET || 'atSecretKey',
    AT_TIMEOUT: process.env.AT_TIMEOUT || '24h',
    RT_SECRET: process.env.RT_SECRET || 'rtSecretKey',
    RT_TIMEOUT: process.env.RT_TIMEOUT || '30d',
    LIMIT_SEND_FORGOT_PASS: parseInt(process.env.LIMIT_SEND_FORGOT_PASS || '5'),
    OTP_EXPIRATION_TIME: parseInt(process.env.OTP_EXPIRATION_TIME || '1'),
    PAGE_DEFAULT: parseInt(process.env.PAGE_DEFAULT || '20'),
    LIMIT_UPLOAD_SIZE: parseInt(process.env.LIMIT_UPLOAD_SIZE || '5'), //mb
    API_URL: process.env.API_URL || 'http://localhost:8003/',
    NC_API_URL: process.env.NC_API_URL || 'https://api.sandbox.namecheap.com/xml.response',
    NC_API_USER: process.env.NC_API_USER || '',
    NC_API_KEY: process.env.NC_API_KEY || '',
    NC_CLIENT_IP: process.env.NC_CLIENT_IP || '',
    STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY || '',
    MAIL_HOST: process.env.MAIL_HOST || '',
    MAIL_PORT: parseInt(process.env.MAIL_PORT || '0'),
    MAIL_USER: process.env.MAIL_USER || '',
    MAIL_PASSWORD: process.env.MAIL_PASSWORD || '',
    MAIL_FROM: process.env.MAIL_FROM || '',
    SUPPORT_MAIL: process.env.SUPPORT_MAIL || '',
    SLACK_WEBHOOK_URL: process.env.SLACK_WEBHOOK_URL || '',
    ROOT_PATH: process.env.ROOT_PATH || '',
    DEVW_API_URL: process.env.DEVW_API_URL || '',
    DESW_API_URL: process.env.DESW_API_URL || '',
    W_API_KEY: process.env.W_API_KEY || '',
    RS_API_URL: process.env.RS_API_URL || '',
    RS_BUNDLE_ID: process.env.RS_BUNDLE_ID || '',
    RS_USERNAME: process.env.RS_USERNAME || '',
    RS_PASSWORD: process.env.RS_PASSWORD || '',
    REQUEST_TIMEOUT: parseInt(process.env.REQUEST_TIMEOUT || '15'),
    WP_ADMIN_PASS: process.env.WP_ADMIN_PASS || '',
    WP_FILE_URL_DEFAULT: process.env.WP_FILE_URL_DEFAULT || '',
    // Cloudflare
    CF_API_TOKEN: process.env.CF_API_TOKEN || '',
    CF_ZONE_ID: process.env.CF_ZONE_ID || '',
    // AI Service
};

export default appConf;
