import { diskStorage, FileFilterCallback, Options } from 'multer';
import { mkdir } from '../commons/helpers/common.helper';
import { BadRequestException } from '@nestjs/common';
import { Request } from 'express';
import { IMAGES_PATH, OTHERS_PATH, UPLOAD_DIR } from '../commons/helpers/file.helper';
import appConf from './app.conf';
import slugify from 'slugify';

export const multerConfig = {
    dest: './' + UPLOAD_DIR, // Thư mục lưu file
    limitSize: appConf.LIMIT_UPLOAD_SIZE * 1024 * 1024, // Giới hạn dung lượng file upload theo MB
};

export const multerOptions: Options = {
    storage: diskStorage({
        destination: (_, file, cb) => {
            let subPath: String;
            //Chia subPath theo mimetype
            if (file.mimetype.match(/\/(jpg|jpeg|png|gif|webp|bmp)$/)) {
                subPath = `/${IMAGES_PATH}`;
            } else {
                subPath = `/${OTHERS_PATH}`;
            }
            // Chia subPath theo ngày
            subPath += `/${new Date().toISOString().slice(0, 10)}`;
            // Tạo thư mục nếu chưa tồn tại
            let uploadPath = multerConfig.dest + subPath;
            mkdir(uploadPath);

            cb(null, uploadPath); // Lưu file vào thư mục chỉ định
        },
        filename: (req, file, cb) => {
            const randomName = Date.now() + '-' + Math.round(Math.random() * 1e9);
            const filename = `${randomName}_${slugify(file.originalname)}`;
            // Gắn vào request để tiện xoá nếu request bị cancel
            if (!req['uploadedFiles']) req['uploadedFiles'] = [];
            req['uploadedFiles'].push({ filename, destination: req['destination'] });
            cb(null, filename);
        },
    }),
    fileFilter: (_: Request, file: Express.Multer.File, cb: FileFilterCallback) => {
        console.log(file.mimetype);
        if (
            file.mimetype.match(/\/(jpg|jpeg|png|gif|webp|bmp)$/) ||
            file.mimetype === 'text/csv' ||
            file.mimetype === 'application/zip' ||
            file.mimetype === 'application/x-zip-compressed' ||
            file.mimetype === 'application/sql' ||
            file.mimetype === 'application/json' ||
            file.mimetype === 'text/plain' ||
            file.mimetype === 'application/octet-stream' ||
            file.originalname.toLowerCase().endsWith('.wpress')
        ) {
            cb(null, true);
        } else {
            cb(new BadRequestException('Invalid file type!') as any, false);
        }
    },
    limits: {
        fileSize: multerConfig.limitSize,
    },
};
