import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { FileEntity } from './file.entity';

@ObjectType('template_components')
@Entity('template_components')
@SearchFields(['name'])
export class TemplateComponentEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column({ type: 'smallint' })
    @Field()
    status_id: ItemStatus;

    @Column({ nullable: true })
    @Field({ nullable: true })
    file_id?: number;

    @ManyToOne(() => FileEntity, { nullable: true, onDelete: 'SET NULL' })
    @JoinColum<PERSON>({ name: 'file_id' })
    @Field(() => FileEntity, { nullable: true })
    file?: FileEntity;
}
