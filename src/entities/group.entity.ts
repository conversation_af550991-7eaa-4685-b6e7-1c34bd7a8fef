import { Column, Entity, ManyToMany } from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ActionEntity } from './action.entity';
import { UserEntity } from './user.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

@ObjectType('groups')
@Entity('groups')
@SearchFields(['name'])
export class GroupEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ nullable: true })
    @Field(() => String, { nullable: true })
    desc?: string;

    @ManyToMany(() => ActionEntity, (action) => action.groups)
    @Field(() => [ActionEntity], { nullable: true })
    actions?: ActionEntity[];

    @ManyToMany(() => UserEntity, (user) => user.groups)
    @Field(() => [UserEntity], { nullable: true })
    users?: UserEntity[];
}
