import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>inTable, ManyToMany, ManyToOne, OneToMany, OneToOne } from 'typeorm';
import { Field, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { FileEntity } from './file.entity';
import { GroupEntity } from './group.entity';
import { PasswordResetEntity } from './password-reset.entity';
import { PersonalTokenEntity } from './personal-token.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { IndustryEntity } from './industry.entity';
import { TemplateEntity } from './template.entity';
import { GraphQLJSON } from 'graphql-type-json';
import { WebsiteEntity } from './website.entity';
import { TransactionEntity } from './transaction.entity';
import { TemplateMetaEntity } from './template-meta.entity';

@Entity('users')
@ObjectType('users')
@SearchFields(['first_name', 'last_name', 'email', 'phone_number', 'full_name'])
export class UserEntity extends BaseEntity {
    @Column()
    @Field()
    first_name: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    last_name?: string;

    @Column({
        generatedType: 'STORED',
        asExpression: `first_name || ' ' || COALESCE(last_name, '')`,
        insert: false,
        update: false,
    })
    @Field()
    full_name: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    phone_number?: string;

    @Column()
    @Field()
    email: string;

    @Column({ nullable: true })
    password?: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    avatar_id?: number;

    @Column({ type: 'smallint' })
    @Field()
    role_id: UserRoles;

    @Column({ type: 'smallint' })
    @Field()
    status_id: UserStatus;

    @Column({ type: 'smallint', nullable: true })
    @Field({ nullable: true })
    gender_id?: Genders;

    @Column({ type: 'timestamptz', nullable: true })
    @Field(() => String, { nullable: true })
    birthday?: Date;

    @Column({ nullable: true })
    @Field({ nullable: true })
    address?: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    uid?: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    industry_id?: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    bio?: string;

    @OneToOne(() => FileEntity, (f) => f.user, { nullable: true, onDelete: 'SET NULL' })
    @JoinColumn({ name: 'avatar_id' })
    @Field(() => FileEntity, { nullable: true })
    avatar?: FileEntity;

    @Column({ type: 'jsonb', nullable: true })
    @Field(() => GraphQLJSON, { nullable: true })
    info?: any;

    @ManyToMany(() => GroupEntity, (group) => group.users)
    @JoinTable({
        name: 'user_groups',
        joinColumn: { name: 'user_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'group_id', referencedColumnName: 'id' },
    })
    @Field(() => [GroupEntity], { nullable: true })
    groups?: GroupEntity[];

    @OneToMany(() => PasswordResetEntity, (passwordReset) => passwordReset.user)
    pws?: PasswordResetEntity[];

    @OneToMany(() => PersonalTokenEntity, (personalToken) => personalToken.user)
    personal_tokens?: PersonalTokenEntity[];

    @ManyToOne(() => IndustryEntity, (i) => i.users)
    @JoinColumn({ name: 'industry_id' })
    @Field(() => IndustryEntity, { nullable: true })
    industry?: IndustryEntity;

    @OneToMany(() => TemplateEntity, (t) => t.designer, { nullable: true, onDelete: 'SET NULL' })
    @Field(() => [TemplateEntity], { nullable: true })
    templates?: TemplateEntity[];

    @ManyToMany(() => TemplateEntity, (t) => t.industries)
    @Field(() => [TemplateEntity], { nullable: true })
    userTemplates?: TemplateEntity[];

    @OneToMany(() => WebsiteEntity, (website) => website.customer)
    @Field(() => [WebsiteEntity], { nullable: true })
    websites?: WebsiteEntity[];

    @OneToMany(() => TransactionEntity, (transaction) => transaction.customer)
    @Field(() => [TransactionEntity], { nullable: true })
    transactions?: TransactionEntity[];

    @OneToMany(() => TemplateMetaEntity, (templateMeta) => templateMeta.user)
    @Field(() => [TemplateMetaEntity], { nullable: true })
    templateMeta?: TemplateMetaEntity[];
}

export enum UserRoles {
    ADMIN = 1,
    CUSTOMER = 2,
    DESIGNER = 3,
}

export enum UserStatus {
    PENDING = 1,
    ACTIVE = 2,
    LOCKED = 3,
}

export enum Genders {
    MALE = 1,
    FEMALE = 2,
    OTHER = 3,
}

registerEnumType(UserRoles, {
    name: 'UserRoles',
});

registerEnumType(UserStatus, {
    name: 'UserStatus',
});

registerEnumType(Genders, {
    name: 'Genders',
});
