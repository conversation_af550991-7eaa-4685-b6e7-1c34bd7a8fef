import { Column, Entity } from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

@ObjectType('master_tags')
@Entity('master_tags')
@SearchFields(['name', 'display_name', 'type'])
export class MasterTagEntity extends BaseEntity {
    @Column({ unique: true })
    @Field()
    name: string;

    @Column()
    @Field()
    type: string;

    @Column()
    @Field()
    display_name: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    tool_tip?: string;
}
