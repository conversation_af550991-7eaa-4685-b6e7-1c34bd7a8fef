import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { TemplateEntity } from './template.entity';
import { UserEntity } from './user.entity';

@ObjectType('template_meta')
@Entity('template_meta')
export class TemplateMetaEntity extends BaseEntity {
    @Column()
    @Field()
    template_id: number;

    @Column()
    @Field()
    user_id: number;

    @Column()
    @Field()
    domain: string;

    @Column()
    @Field()
    page_slug: string;

    @Column()
    @Field()
    meta_key: string;

    @Column()
    @Field()
    meta_value: string;

    @ManyToOne(() => TemplateEntity, (template) => template.templateMeta)
    @JoinColumn({ name: 'template_id' })
    @Field(() => TemplateEntity)
    template: TemplateEntity;

    @ManyToOne(() => UserEntity, (user) => user.templateMeta)
    @JoinColumn({ name: 'user_id' })
    @Field(() => UserEntity)
    user: UserEntity;
}
