import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { WebsiteEntity } from './website.entity';
import { FileEntity } from './file.entity';

@ObjectType('backup_websites')
@Entity('backup_websites')
export class BackupWebsiteEntity extends BaseEntity {
    @Column()
    @Field()
    website_id: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    file_id?: number;

    @Column({ type: 'boolean', default: false })
    @Field()
    is_request: boolean;

    @Column({ type: 'boolean', default: false })
    @Field()
    is_process_restore: boolean;

    @ManyToOne(() => WebsiteEntity, { nullable: false })
    @JoinColumn({ name: 'website_id' })
    @Field(() => WebsiteEntity)
    website: WebsiteEntity;

    @ManyToOne(() => FileEntity, { nullable: true })
    @JoinColumn({ name: 'file_id' })
    @Field(() => FileEntity, { nullable: true })
    file?: FileEntity;
}
