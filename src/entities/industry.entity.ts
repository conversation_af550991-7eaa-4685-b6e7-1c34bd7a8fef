import { Column, <PERSON>tity, ManyToMany, OneToMany } from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { UserEntity } from './user.entity';
import { TemplateEntity } from './template.entity';

@ObjectType('industries')
@Entity('industries')
export class IndustryEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column()
    @Field()
    display_order: number;

    @Column({ type: 'smallint' })
    @Field()
    status_id: ItemStatus;

    @OneToMany(() => UserEntity, (user) => user.industry)
    @Field(() => [UserEntity], { nullable: true })
    users?: UserEntity[];

    @ManyToMany(() => TemplateEntity, (t) => t.industries)
    @Field(() => [TemplateEntity], { nullable: true })
    templates?: TemplateEntity[];
}
