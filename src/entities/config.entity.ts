import { Column, Entity } from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

@Entity('configs')
@ObjectType('configs')
@SearchFields(['code', 'name', 'value'])
export class ConfigEntity extends BaseEntity {
    @Column({ unique: true })
    @Field()
    code: string;

    @Column()
    @Field()
    name: string;

    @Column({ type: 'text' })
    @Field()
    value: string;
}
