import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { Field, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { UserEntity } from './user.entity';
import { TemplateEntity } from './template.entity';
import GraphQLJSON from 'graphql-type-json';
import { TransactionEntity } from './transaction.entity';

export enum WebsiteStatus {
    WAITING = 1,
    INSTALLING = 2,
    INSTALLED = 3,
    ACTIVE = 4,
    EXPIRED = 5,
    DESTROYED = 6,
    DISABLED = 7,
}

registerEnumType(WebsiteStatus, {
    name: 'WebsiteStatus',
});

@ObjectType('websites')
@Entity('websites')
@SearchFields(['domain', 'custom_domain'])
export class WebsiteEntity extends BaseEntity {
    @Column()
    @Field()
    customer_id: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    template_id?: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    domain?: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    custom_domain?: string;

    @Column({ type: 'jsonb', nullable: true })
    @Field(() => GraphQLJSON, { nullable: true })
    info?: any;

    @Column({ type: 'smallint' })
    @Field()
    status_id: WebsiteStatus;

    @Column({ type: 'boolean', default: false })
    @Field()
    is_dead: boolean;

    @Column({ type: 'boolean', default: false })
    @Field()
    is_process_backup: boolean;

    @ManyToOne(() => UserEntity, (user) => user.websites)
    @JoinColumn({ name: 'customer_id' })
    @Field(() => UserEntity)
    customer: UserEntity;

    @ManyToOne(() => TemplateEntity, (template) => template.websites, { nullable: true })
    @JoinColumn({ name: 'template_id' })
    @Field(() => TemplateEntity, { nullable: true })
    template?: TemplateEntity;

    @OneToMany(() => TransactionEntity, (transaction) => transaction.website)
    @Field(() => [TransactionEntity], { nullable: true })
    transactions?: TransactionEntity[];
}
