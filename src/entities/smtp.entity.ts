import { Field, ObjectType } from '@nestjs/graphql';
import { Column, Entity } from 'typeorm';
import { BaseEntity } from '../commons/bases/base.entity';
import GraphQLJSON from 'graphql-type-json';

export interface SmtpConfig {
    host: string;
    port: number;
    ssl: boolean;
    username: string;
    password: string;
}

@Entity('smtps')
@ObjectType('smtps')
export class SmtpEntity extends BaseEntity {
    @Column()
    @Field()
    customer_id: number;

    @Column({ type: 'jsonb' })
    @Field(() => GraphQLJSON)
    smtp_config: SmtpConfig;
}
