import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>in<PERSON><PERSON><PERSON><PERSON>, JoinTable, ManyToMany, ManyToOne, OneToMany, OneToOne } from 'typeorm';
import { Field, GraphQLISODateTime, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { GraphQLJSON } from 'graphql-type-json';
import { UserEntity } from './user.entity';
import { IndustryEntity } from './industry.entity';
import { FileEntity } from './file.entity';
import { WebsiteEntity } from './website.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { TemplateMetaEntity } from './template-meta.entity';
import { CronTemplateEntity } from './cron-template.entity';

@ObjectType('templates')
@Entity('templates')
@SearchFields(['name', 'industries.name'])
export class TemplateEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column({ type: 'smallint' })
    @Field()
    status_id: ItemStatus;

    @Column({ nullable: true })
    @Field({ nullable: true })
    designer_id?: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    image_id?: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    csv_file_id?: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    code_file_id?: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    template_file_id?: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    sql_file_id?: number;

    @Column()
    @Field()
    is_multiple?: boolean;

    @Column({ nullable: true })
    @Field(() => GraphQLISODateTime, { nullable: true })
    approved_date?: Date;

    @Column({ type: 'jsonb', nullable: true })
    @Field(() => GraphQLJSON, { nullable: true })
    info?: any;

    @Column({ type: 'boolean', default: false })
    @Field(() => Boolean)
    is_kit: boolean;

    @Column({ nullable: true })
    @Field({ nullable: true })
    reject_reason?: string;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    reject_file_id?: number;

    /* relations */

    @ManyToOne(() => UserEntity, (u) => u.templates)
    @JoinColumn({ name: 'designer_id' })
    @Field(() => UserEntity, { nullable: true })
    designer?: UserEntity;

    @OneToOne(() => FileEntity, (f) => f.templateImage)
    @JoinColumn({ name: 'image_id' })
    @Field(() => FileEntity, { nullable: true })
    image?: FileEntity;

    @OneToOne(() => FileEntity, (f) => f.templateCsvFile)
    @JoinColumn({ name: 'csv_file_id' })
    @Field(() => FileEntity, { nullable: true })
    csvFile?: FileEntity;

    @OneToOne(() => FileEntity, (f) => f.templateCodeFile)
    @JoinColumn({ name: 'code_file_id' })
    @Field(() => FileEntity, { nullable: true })
    codeFile?: FileEntity;

    @OneToOne(() => FileEntity, (f) => f.templateFile)
    @JoinColumn({ name: 'template_file_id' })
    @Field(() => FileEntity, { nullable: true })
    templateFile?: FileEntity;

    @OneToOne(() => FileEntity, (f) => f.sqlFile)
    @JoinColumn({ name: 'sql_file_id' })
    @Field(() => FileEntity, { nullable: true })
    sqlFile?: FileEntity;

    @ManyToMany(() => IndustryEntity, (i) => i.templates)
    @JoinTable({
        name: 'template_industries',
        joinColumn: { name: 'template_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'industry_id', referencedColumnName: 'id' },
    })
    @Field(() => [IndustryEntity], { nullable: true })
    industries?: IndustryEntity[];

    @ManyToMany(() => UserEntity, (i) => i.userTemplates)
    @JoinTable({
        name: 'user_templates',
        joinColumn: { name: 'template_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'user_id', referencedColumnName: 'id' },
    })
    @Field(() => [UserEntity], { nullable: true })
    user?: UserEntity[];

    @OneToMany(() => WebsiteEntity, (website) => website.template)
    @Field(() => [WebsiteEntity], { nullable: true })
    websites?: WebsiteEntity[];

    @OneToMany(() => TemplateMetaEntity, (templateMeta) => templateMeta.template)
    @Field(() => [TemplateMetaEntity], { nullable: true })
    templateMeta?: TemplateMetaEntity[];

    @OneToMany(() => CronTemplateEntity, (cronTemplate) => cronTemplate.template)
    @Field(() => [CronTemplateEntity], { nullable: true })
    cronTemplates?: CronTemplateEntity[];

    @OneToOne(() => FileEntity, (f) => f.templateRejectFile)
    @JoinColumn({ name: 'reject_file_id' })
    @Field(() => FileEntity, { nullable: true })
    rejectFile?: FileEntity;
}
