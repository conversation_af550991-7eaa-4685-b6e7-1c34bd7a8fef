import { Column, <PERSON>tity, OneToMany, OneToOne } from 'typeorm';
import { Field, Float, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { ItemStatus } from '../commons/enums.common';
import { GraphQLJSON } from 'graphql-type-json';
import { TransactionEntity } from './transaction.entity';

export enum PlanType {
    TRIAL = 1,
    BASIC = 2,
    PREMIUM = 3,
}

@ObjectType('subscription_plans')
@Entity('subscription_plans')
@SearchFields(['name'])
export class SubscriptionPlanEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ nullable: true })
    @Field(() => String, { nullable: true })
    desc?: string;

    @Column({ type: 'smallint' })
    @Field()
    status_id: ItemStatus;

    @Column({ type: 'numeric', precision: 8, scale: 2 })
    @Field(() => Float)
    price: number;

    @Column()
    @Field()
    display_order: number;

    @Column({ type: 'jsonb', nullable: true })
    @Field(() => GraphQLJSON, { nullable: true })
    features?: IFeature[];

    @Column({ type: 'smallint', default: PlanType.TRIAL })
    @Field(() => Number)
    type_id: PlanType;

    @Column({ type: 'smallint', nullable: true })
    @Field(() => Number, { nullable: true })
    trial_days?: number;

    @Column({ type: 'numeric', precision: 8, scale: 2, nullable: true })
    @Field(() => Float, { nullable: true })
    original_price?: number;

    @Column({ nullable: true })
    @Field(() => String, { nullable: true })
    sub_desc?: string;

    @Column({ type: 'jsonb', nullable: true })
    @Field(() => GraphQLJSON, { nullable: true })
    highlights?: any;

    @Column({ nullable: true })
    @Field(() => String, { nullable: true })
    woo_subscription_id?: string;

    @OneToMany(() => TransactionEntity, (transaction) => transaction.plan)
    transactions: TransactionEntity[];
}

export interface IFeature {
    id: number;
    value: string;
}
