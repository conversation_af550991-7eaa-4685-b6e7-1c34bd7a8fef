import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { TemplateEntity } from './template.entity';
import { WebsiteStatus } from './website.entity';
import { GraphQLJSON } from 'graphql-type-json';

@ObjectType('cron_templates')
@Entity('cron_templates')
export class CronTemplateEntity extends BaseEntity {
    @Column({ nullable: true })
    @Field()
    template_id?: number;

    @Column({ unique: true })
    @Field()
    domain: string;

    @Column({ type: 'smallint' })
    @Field()
    status_id: WebsiteStatus;

    @Column({ type: 'jsonb', nullable: true })
    @Field(() => GraphQLJSON, { nullable: true })
    deploy_status?: any;

    @Column({ nullable: true })
    @Field({ nullable: true })
    job_id?: string;

    @ManyToOne(() => TemplateEntity, (template) => template.cronTemplates)
    @JoinColumn({ name: 'template_id' })
    @Field(() => TemplateEntity, { nullable: true })
    template?: TemplateEntity;
}
