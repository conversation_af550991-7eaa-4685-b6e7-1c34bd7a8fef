import { Column, Entity, OneToOne } from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { UserEntity } from './user.entity';
import { pathToUrl } from '../commons/helpers/file.helper';
import { TemplateEntity } from './template.entity';
import { TemplateComponentEntity } from './template-component.entity';

@ObjectType('files')
@Entity('files')
export class FileEntity extends BaseEntity {
    @Column()
    @Field()
    file_name: string;

    @Column({
        transformer: {
            to(value) {
                return value.toString();
            },
            from(value) {
                return value ? pathToUrl(value) : value;
            },
        },
    })
    @Field()
    file_url: string;

    @Column({ type: 'integer' })
    @Field()
    file_size: number;

    @Column()
    @Field()
    mime_type: string;

    @OneToOne(() => UserEntity, (u) => u.avatar)
    // @Field(() => UserEntity, { nullable: true })
    user?: UserEntity;

    @OneToOne(() => TemplateEntity, (t) => t.image)
    // @Field(() => TemplateEntity, { nullable: true })
    templateImage?: TemplateEntity;

    @OneToOne(() => TemplateEntity, (t) => t.csvFile)
    templateCsvFile?: TemplateEntity;

    @OneToOne(() => TemplateEntity, (t) => t.codeFile)
    templateCodeFile?: TemplateEntity;

    @OneToOne(() => TemplateEntity, (t) => t.templateFile)
    templateFile?: TemplateEntity;

    @OneToOne(() => TemplateEntity, (t) => t.sqlFile)
    sqlFile?: TemplateEntity;

    @OneToOne(() => TemplateComponentEntity, (tc) => tc.file)
    templateComponent?: TemplateComponentEntity;

    @OneToOne(() => TemplateEntity, (t) => t.rejectFile)
    templateRejectFile?: TemplateEntity;
}
