import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { Field, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { UserEntity } from './user.entity';
import { WebsiteEntity } from './website.entity';

export enum TicketType {
    WEBSITE_DOWN = 1,
    DOMAIN = 2,
    OTHER = 3,
}

export enum TicketStatus {
    PENDING = 1,
    PROCESSING = 2,
    FINISH = 3,
}

@ObjectType('tickets')
@Entity('tickets')
@SearchFields(['title', 'content', 'feedback'])
export class TicketEntity extends BaseEntity {
    @Column()
    @Field()
    customer_id: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    website_id?: number;

    @Column()
    @Field()
    title: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    content?: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    feedback?: string;

    @Column({ type: 'smallint' })
    @Field()
    type_id: TicketType;

    @Column({ type: 'smallint' })
    @Field()
    status_id: TicketStatus;

    @ManyToOne(() => UserEntity, { nullable: false })
    @JoinColumn({ name: 'customer_id' })
    @Field(() => UserEntity)
    customer: UserEntity;

    @ManyToOne(() => WebsiteEntity, { nullable: true })
    @JoinColumn({ name: 'website_id' })
    @Field(() => WebsiteEntity, { nullable: true })
    website?: WebsiteEntity;
}

registerEnumType(TicketType, {
    name: 'TicketType',
});

registerEnumType(TicketStatus, {
    name: 'TicketStatus',
});
