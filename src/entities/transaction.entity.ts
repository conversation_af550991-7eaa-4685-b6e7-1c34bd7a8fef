import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { Field, Float, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { UserEntity } from './user.entity';
import { WebsiteEntity } from './website.entity';
import { GraphQLJSON } from 'graphql-type-json';
import { SubscriptionPlanEntity } from './subscription-plan.entity';

export enum TransactionType {
    WEBSITE = 1,
    DOMAIN = 2,
}

export enum SubscriptionStatus {
    PAID = 1,
    UNPAID = 2,
}

@ObjectType('transactions')
@Entity('transactions')
export class TransactionEntity extends BaseEntity {
    @Column()
    @Field()
    customer_id: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    plan_id?: number;

    @Column({ type: 'smallint' })
    @Field()
    type_id: TransactionType;

    @Column({ type: 'decimal', precision: 8, scale: 2 })
    @Field(() => Float)
    price: number;

    @Column({ type: 'smallint' })
    @Field()
    status_id: SubscriptionStatus;

    @Column({ type: 'date' })
    @Field(() => String)
    start_date: Date;

    @Column({ type: 'date' })
    @Field(() => String)
    end_date: Date;

    @Column({ nullable: true })
    @Field({ nullable: true })
    website_id?: number;

    @Column({ type: 'jsonb' })
    @Field(() => GraphQLJSON)
    info: any;

    @ManyToOne(() => UserEntity)
    @JoinColumn({ name: 'customer_id' })
    customer: UserEntity;

    @ManyToOne(() => WebsiteEntity, (website) => website.transactions, { nullable: true })
    @JoinColumn({ name: 'website_id' })
    @Field(() => WebsiteEntity, { nullable: true })
    website?: WebsiteEntity;

    @ManyToOne(() => SubscriptionPlanEntity, (plan) => plan.transactions, { nullable: true })
    @JoinColumn({ name: 'plan_id' })
    @Field(() => SubscriptionPlanEntity, { nullable: true })
    plan?: SubscriptionPlanEntity;
}

registerEnumType(TransactionType, {
    name: 'TransactionType',
});

registerEnumType(SubscriptionStatus, {
    name: 'SubscriptionStatus',
});
