import { Column, Entity, Index } from 'typeorm';
import { BaseEntity } from '../commons/bases/base.entity';
import { Field, ObjectType } from '@nestjs/graphql';
import { GraphQLJSON } from 'graphql-type-json';

@ObjectType()
@Entity('deployment_statuses')
export class DeploymentStatusEntity extends BaseEntity {
    @Field()
    @Column({ type: 'varchar', length: 255 })
    @Index()
    domain: string;

    @Field()
    @Column({ type: 'varchar', length: 100 })
    step: string;

    @Field()
    @Column({ type: 'boolean', default: true })
    success: boolean;

    @Field(() => GraphQLJSON, { nullable: true })
    @Column({ type: 'jsonb', nullable: true, default: {} })
    extra: Record<string, any>;

    @Field()
    @Column({ type: 'varchar', length: 50, default: () => 'to_char(NOW(), \'YYYY-MM-DD"T"HH24:MI:SS"Z"\')' })
    timestamp: string;
}
