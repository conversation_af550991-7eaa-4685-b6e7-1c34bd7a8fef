import { Column, Entity } from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { ItemStatus } from '../commons/enums.common';

@ObjectType('website_styles')
@Entity('website_styles')
@SearchFields(['name'])
export class WebsiteStyleEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ nullable: true })
    @Field(() => String, { nullable: true })
    desc?: string;

    @Column()
    @Field()
    display_order: number;

    @Column({ type: 'smallint' })
    @Field()
    status_id: ItemStatus;
}
