import { Module } from '@nestjs/common';
import { TemplateComponentsService } from './template-components.service';
import { TemplateComponentsResolver } from './template-components.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TemplateComponentEntity } from '../../entities/template-component.entity';
import { FileEntity } from '../../entities/file.entity';

@Module({
    imports: [TypeOrmModule.forFeature([TemplateComponentEntity, FileEntity])],
    providers: [TemplateComponentsService, TemplateComponentsResolver],
    exports: [TemplateComponentsService],
})
export class TemplateComponentsModule {}
