import { <PERSON><PERSON><PERSON>, <PERSON>t, <PERSON><PERSON>, Query, <PERSON>solve<PERSON><PERSON>, Resolver } from '@nestjs/graphql';
import { TemplateComponentEntity } from '../../entities/template-component.entity';
import { TemplateComponentsService } from './template-components.service';
import { DataLoaderService } from '../../data-loaders/data-loaders.service';
import { TemplateComponentSaveInputDto } from './dtos/template-component-save-input.dto';
import { AuthMutation } from '../../commons/decorators/graphql.decorators';
import { Roles } from '../../commons/decorators/roles.decorator';
import { UserEntity, UserRoles } from '../../entities/user.entity';
import { AuthUser } from '../auth/auth.decorator';
import { FileEntity } from '../../entities/file.entity';
import { NotFoundException } from '@nestjs/common';
import { TemplateComponentModel } from './models/template-component.model';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { IPaginatedType } from '../../commons/bases/base.model';

@Resolver(TemplateComponentEntity)
export class TemplateComponentsResolver {
    constructor(
        private readonly templateComponentsService: TemplateComponentsService,
        private readonly dataLoaderService: DataLoaderService
    ) {}

    @ResolveField(() => FileEntity, { nullable: true })
    async file(@Parent() templateComponent: TemplateComponentEntity): Promise<FileEntity | null> {
        if (!templateComponent.file_id) return null;
        return this.dataLoaderService.relationBatchOne(FileEntity).load(templateComponent.file_id);
    }

    @Query(() => TemplateComponentModel, { name: 'template_component_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<TemplateComponentEntity>> {
        return this.templateComponentsService.search(body);
    }

    @Query(() => TemplateComponentEntity, { name: 'template_component_view' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<TemplateComponentEntity> {
        return this.templateComponentsService.findOne(id).then((item) => {
            if (!item) throw new NotFoundException();
            return item;
        });
    }

    @AuthMutation(() => TemplateComponentEntity, { name: 'template_component_create' })
    @Roles([UserRoles.ADMIN])
    async create(
        @Args('body') body: TemplateComponentSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<TemplateComponentEntity> {
        return this.templateComponentsService.saveTemplateComponent({ ...body, created_by: auth.id });
    }

    @AuthMutation(() => TemplateComponentEntity, { name: 'template_component_update' })
    @Roles([UserRoles.ADMIN])
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: TemplateComponentSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<TemplateComponentEntity> {
        return this.templateComponentsService.saveTemplateComponent({ ...body, updated_by: auth.id }, id);
    }

    @AuthMutation(() => Boolean, { name: 'template_component_delete' })
    @Roles([UserRoles.ADMIN])
    async delete(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.templateComponentsService.softDelete(id, auth.id);
        return true;
    }
}
