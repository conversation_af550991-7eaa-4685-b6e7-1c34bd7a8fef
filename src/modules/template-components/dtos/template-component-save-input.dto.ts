import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsInt, IsNotEmpty, IsOptional, IsPositive, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { ItemStatus } from '../../../commons/enums.common';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { FileEntity } from '../../../entities/file.entity';

@InputType()
export class TemplateComponentSaveInputDto extends BaseUpdateInputDto {
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @Field()
    name: string;

    @IsOptional()
    @IsString()
    @Field({ nullable: true })
    desc?: string;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(ItemStatus)
    @Field(() => Int)
    status_id: ItemStatus;

    @IsOptional()
    @IsInt()
    @IsPositive()
    @IdExists(FileEntity)
    @Field(() => Int, { nullable: true })
    file_id?: number;
}

export interface ITemplateComponentSaveInput {
    name: string;
    desc?: string;
    status_id: ItemStatus;
    file_id?: number;
    created_by?: number;
    updated_by?: number;
}
