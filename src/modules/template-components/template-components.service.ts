import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { BaseService } from '../../commons/bases/base.service';
import { TemplateComponentEntity } from '../../entities/template-component.entity';
import { In, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { ITemplateComponentSaveInput } from './dtos/template-component-save-input.dto';
import { ItemStatus } from '../../commons/enums.common';
import { CronTemplateEntity } from '../../entities/cron-template.entity';
import { WebsiteStatus } from '../../entities/website.entity';
import axios from 'axios';
import appConf from '../../configs/app.conf';
import { FileEntity } from '../../entities/file.entity';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { downloadFileToTemp } from '../../commons/helpers/file.helper';
const AdmZip = require('adm-zip');

@Injectable()
export class TemplateComponentsService extends BaseService<TemplateComponentEntity> {
    constructor(
        @InjectRepository(TemplateComponentEntity)
        public readonly repo: Repository<TemplateComponentEntity>
    ) {
        super(repo);
    }

    async saveTemplateComponent(body: ITemplateComponentSaveInput, id?: number): Promise<TemplateComponentEntity> {
        if (body.file_id) {
            const file = await this.repo.manager.findOne(FileEntity, { where: { id: body.file_id } });
            if (!file) throw new BadRequestException('File not found');
            await this.extractAndValidateComponentZip(file.file_url);
        }

        const templateComponent = id ? await this.updateOne(id, body) : await this.create(body);

        if (body.status_id === ItemStatus.ACTIVE && body.file_id) {
            const [cronTemplates, file] = await Promise.all([
                this.repo.manager.find(CronTemplateEntity, {
                    where: {
                        status_id: In([WebsiteStatus.ACTIVE, WebsiteStatus.INSTALLED]),
                        template: {
                            is_kit: true,
                        },
                    },
                }),
                this.repo.manager.findOne(FileEntity, { where: { id: body.file_id } }),
            ]);

            if (!file) throw new InternalServerErrorException('File not found');

            cronTemplates.map((cronTemplate) =>
                axios.post(
                    `https://${cronTemplate.domain}/wp-json/template-toolkit/v1/elementor-templates/import`,
                    {
                        file_url: file.file_url,
                        template_type: 'auto',
                    },
                    {
                        headers: {
                            Authorization: `Bearer ${appConf.W_API_KEY}`,
                            'Content-Type': 'application/json',
                        },
                    }
                )
            );
        }

        return templateComponent;
    }

    /**
     * Extract and validate component zip file
     * giải nén, check trong đó, nếu toàn là file JSON thì OK
     * @param filePath
     * @private
     */
    private async extractAndValidateComponentZip(filePath: string): Promise<void> {
        const zipPath = await downloadFileToTemp(filePath);
        const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'component-'));

        try {
            const zip = new AdmZip(zipPath);
            zip.extractAllTo(tempDir, true);

            const files = fs.readdirSync(tempDir);

            const isAllJson = files.every((file) => file.endsWith('.json'));

            if (!isAllJson) {
                throw new BadRequestException('Invalid file: All files must be .json');
            }
        } finally {
            fs.rmSync(tempDir, { recursive: true, force: true });
            fs.unlinkSync(zipPath);
        }
    }
}
