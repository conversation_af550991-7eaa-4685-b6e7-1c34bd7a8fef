import { Args, Query, Resolver, Mutation } from '@nestjs/graphql';
import { GraphQLJSON } from 'graphql-type-json';
import { CloudflareAxios } from '../../axios/cloudflare.axios';
import { UseGuards } from '@nestjs/common';
import { WebhookTokenGuard } from '../../commons/guards/webhook-token.guard';
import { DnsRecord, CreateDnsRecordInput, UpdateDnsRecordInput } from './dtos/dns-record.dto';
import { Zone, CreateZoneInput, DeleteZoneResponse } from './dtos/zone.dto';

@Resolver()
@UseGuards(WebhookTokenGuard)
export class WebhookCloudflareResolver {
    constructor(private readonly cloudflareAxios: CloudflareAxios) {}

    @Query(() => [String], {
        name: 'webhooks_cloudflare_get_name_servers',
        description: 'Get name servers for a domain from Cloudflare',
    })
    async getNameServers(@Args('domain') domain: string): Promise<string[]> {
        return this.cloudflareAxios.getNameServers(domain);
    }

    @Query(() => GraphQLJSON, {
        name: 'webhooks_cloudflare_get_zone_info',
        description: 'Get complete zone information for a domain from Cloudflare',
    })
    async getZoneInfo(@Args('domain') domain: string) {
        return this.cloudflareAxios.getZoneByName(domain);
    }

    @Query(() => [DnsRecord], {
        name: 'webhooks_cloudflare_get_dns_records',
        description: 'Get all DNS records for a domain',
    })
    async getDnsRecords(@Args('domain') domain: string): Promise<DnsRecord[]> {
        return this.cloudflareAxios.getDnsRecords(domain);
    }

    @Query(() => DnsRecord, {
        name: 'webhooks_cloudflare_get_dns_record',
        description: 'Get a specific DNS record by ID',
    })
    async getDnsRecord(@Args('domain') domain: string, @Args('recordId') recordId: string): Promise<DnsRecord> {
        return this.cloudflareAxios.getDnsRecord(domain, recordId);
    }

    @Mutation(() => DnsRecord, {
        name: 'webhooks_cloudflare_create_dns_record',
        description: 'Create a new DNS record',
    })
    async createDnsRecord(@Args('input') input: CreateDnsRecordInput): Promise<DnsRecord> {
        return this.cloudflareAxios.createDnsRecord(input);
    }

    @Mutation(() => DnsRecord, {
        name: 'webhooks_cloudflare_update_dns_record',
        description: 'Update an existing DNS record',
    })
    async updateDnsRecord(@Args('input') input: UpdateDnsRecordInput): Promise<DnsRecord> {
        return this.cloudflareAxios.updateDnsRecord(input);
    }

    @Mutation(() => Boolean, {
        name: 'webhooks_cloudflare_delete_dns_record',
        description: 'Delete a DNS record',
    })
    async deleteDnsRecord(@Args('domain') domain: string, @Args('recordId') recordId: string): Promise<boolean> {
        return this.cloudflareAxios.deleteDnsRecord(domain, recordId);
    }

    @Mutation(() => Zone, {
        name: 'webhooks_cloudflare_create_zone',
        description: 'Create a new zone in Cloudflare',
    })
    async createZone(@Args('input') input: CreateZoneInput): Promise<Zone> {
        return this.cloudflareAxios.createZone(input);
    }

    @Mutation(() => DeleteZoneResponse, {
        name: 'webhooks_cloudflare_delete_zone_by_name',
        description: 'Delete a zone by domain name from Cloudflare',
    })
    async deleteZoneByName(@Args('domain') domain: string): Promise<DeleteZoneResponse> {
        return this.cloudflareAxios.deleteZoneByName(domain);
    }
}
