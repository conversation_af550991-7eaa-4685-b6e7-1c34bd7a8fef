import { InputType, Field, ObjectType } from '@nestjs/graphql';
import { IsString, IsOptional, IsNumber, IsBoolean } from 'class-validator';
import { IsTTL } from '../../../commons/validators/ttl.validator';

@InputType()
export class CreateDnsRecordInput {
    @Field()
    @IsString()
    domain: string;

    @Field()
    @IsString()
    type: string; // A, AAAA, CNAME, MX, TXT, etc.

    @Field()
    @IsString()
    name: string;

    @Field()
    @IsString()
    content: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    @IsTTL()
    ttl?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    priority?: number;

    @Field({ nullable: true, defaultValue: false })
    @IsOptional()
    @IsBoolean()
    proxied?: boolean;
}

@InputType()
export class UpdateDnsRecordInput {
    @Field()
    @IsString()
    domain: string;

    @Field()
    @IsString()
    recordId: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    type?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    name?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    content?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    @IsTTL()
    ttl?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    priority?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    proxied?: boolean;
}

@ObjectType()
export class DnsRecord {
    @Field()
    id: string;

    @Field()
    type: string;

    @Field()
    name: string;

    @Field()
    content: string;

    @Field()
    ttl: number;

    @Field({ nullable: true })
    priority?: number;

    @Field()
    proxied: boolean;

    @Field()
    zone_id: string;

    @Field()
    zone_name: string;

    @Field()
    created_on: string;

    @Field()
    modified_on: string;
}
