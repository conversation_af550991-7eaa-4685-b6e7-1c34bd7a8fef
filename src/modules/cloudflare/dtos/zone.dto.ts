import { InputType, Field, ObjectType } from '@nestjs/graphql';
import { IsString, IsOptional, IsBoolean } from 'class-validator';

@InputType()
export class CreateZoneInput {
    @Field()
    @IsString()
    name: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    type?: string; // "full" (default) or "partial"

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    jump_start?: boolean;
}

@ObjectType()
export class ZoneAccount {
    @Field()
    id: string;

    @Field()
    name: string;
}

@ObjectType()
export class ZoneMeta {
    @Field()
    step: number;

    @Field()
    custom_certificate_quota: number;

    @Field()
    page_rule_quota: number;

    @Field()
    phishing_detected: boolean;

    @Field({ nullable: true })
    cdn_only?: boolean;

    @Field({ nullable: true })
    dns_only?: boolean;

    @Field({ nullable: true })
    wildcard_proxiable?: boolean;

    @Field({ nullable: true })
    multiple_railguns_allowed?: boolean;
}

@ObjectType()
export class ZoneOwner {
    @Field()
    id: string;

    @Field()
    type: string;

    @Field({ nullable: true })
    name?: string;

    @Field({ nullable: true })
    email?: string;
}

@ObjectType()
export class ZonePlan {
    @Field()
    id: string;

    @Field({ nullable: true })
    name?: string;

    @Field({ nullable: true })
    price?: number;

    @Field({ nullable: true })
    currency?: string;

    @Field({ nullable: true })
    frequency?: string;

    @Field({ nullable: true })
    is_subscribed?: boolean;

    @Field({ nullable: true })
    can_subscribe?: boolean;

    @Field({ nullable: true })
    legacy_id?: string;

    @Field({ nullable: true })
    legacy_discount?: boolean;

    @Field({ nullable: true })
    externally_managed?: boolean;
}

@ObjectType()
export class ZoneTenant {
    @Field()
    id: string;

    @Field({ nullable: true })
    name?: string;
}

@ObjectType()
export class ZoneTenantUnit {
    @Field()
    id: string;
}

@ObjectType()
export class DeleteZoneResponse {
    @Field()
    success: boolean;

    @Field()
    message: string;
}
@ObjectType()
export class Zone {
    @Field()
    id: string;

    @Field()
    name: string;

    @Field(() => [String])
    name_servers: string[];

    @Field({ nullable: true })
    status?: string; // "initializing" | "pending" | "active" | "moved"

    @Field({ nullable: true })
    paused?: boolean;

    @Field({ nullable: true })
    type?: string; // "full" | "partial"

    @Field()
    development_mode: number;

    @Field(() => [String])
    original_name_servers: string[];

    @Field({ nullable: true })
    original_registrar?: string;

    @Field({ nullable: true })
    original_dnshost?: string;

    @Field()
    modified_on: string;

    @Field()
    created_on: string;

    @Field({ nullable: true })
    activated_on?: string;

    @Field(() => ZoneAccount)
    account: ZoneAccount;

    @Field(() => ZoneMeta)
    meta: ZoneMeta;

    @Field(() => ZoneOwner)
    owner: ZoneOwner;

    @Field(() => ZonePlan, { nullable: true })
    plan?: ZonePlan;

    @Field({ nullable: true })
    cname_suffix?: string;

    @Field(() => [String], { nullable: true })
    permissions?: string[];

    @Field(() => ZoneTenant, { nullable: true })
    tenant?: ZoneTenant;

    @Field(() => ZoneTenantUnit, { nullable: true })
    tenant_unit?: ZoneTenantUnit;

    @Field(() => [String], { nullable: true })
    vanity_name_servers?: string[];

    @Field({ nullable: true })
    verification_key?: string;
}
