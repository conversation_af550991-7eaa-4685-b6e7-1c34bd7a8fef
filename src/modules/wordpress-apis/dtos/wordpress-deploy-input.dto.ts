import { Field, InputType } from '@nestjs/graphql';
import { ArrayNotEmpty, IsArray, IsNotEmpty, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { DomainInputDto } from './domain-input.dto';

@InputType()
export class WordpressDeployInputDto extends DomainInputDto {
    @IsArray()
    @ArrayNotEmpty()
    @IsString({ each: true })
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED, each: true })
    @Field(() => [String])
    plugins: string[];
}
