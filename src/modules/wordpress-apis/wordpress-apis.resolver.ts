import { ReviewsieuxeAxios } from '../../axios/reviewsieuxe.axios';
import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { WordpressDeployInputDto } from './dtos/wordpress-deploy-input.dto';
import { DomainInputDto } from './dtos/domain-input.dto';
import { NotFoundException, UseGuards } from '@nestjs/common';
import { GraphQLJSON } from 'graphql-type-json';
import { WpcliInputDto } from './dtos/wpcli-input.dto';
import { WebhookTokenGuard } from '../../commons/guards/webhook-token.guard';

//@AuthResolver()
@Resolver()
@UseGuards(WebhookTokenGuard)
export class WordpressApisResolver {
    constructor(private readonly rsAxios: ReviewsieuxeAxios) {}

    @Mutation(() => Boolean, { name: 'wordpress_deploy' })
    async deploy(@Args('body') body: WordpressDeployInputDto): Promise<boolean> {
        await this.rsAxios.deploy(body.domain, body.plugins);
        return true;
    }

    @Mutation(() => <PERSON>olean, { name: 'wordpress_delete' })
    async delete(@Args('body') body: DomainInputDto): Promise<boolean> {
        await this.rsAxios.delete(body.domain);
        return true;
    }

    @Query(() => GraphQLJSON, { name: 'wordpress_status' })
    async view(@Args('domain', { type: () => String }) domain: string) {
        return this.rsAxios.checkStatus(domain).then((res) => {
            if (!res) {
                throw new NotFoundException();
            }
            return res;
        });
    }

    @Mutation(() => GraphQLJSON, { name: 'wordpress_wpcli' })
    async wpcli(@Args('body') body: WpcliInputDto): Promise<boolean> {
        await this.rsAxios.wpcliAsync(body.domain, body.command);
        return true;
    }
}
