import { Field, InputType, Int } from '@nestjs/graphql';
import { IsArray, IsEmail, IsNotEmpty, IsNumber, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';

@InputType()
export class EmailFieldDto {
    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    label: string;

    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    value: string;
}

@InputType()
export class WebhookWebsiteSendEmailInputDto {
    @Field(() => Int)
    @IsNumber()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    website_id: number;

    @Field()
    @IsEmail({}, { message: 'Invalid email format' })
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    customer_email: string;

    @Field(() => [EmailFieldDto])
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => EmailFieldDto)
    fields: EmailFieldDto[];
}
