import { Field, InputType } from '@nestjs/graphql';
import { IsNotEmpty, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';

@InputType()
export class WebhookUserResetPassInputDto {
    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    email: string;

    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    password: string;
}
