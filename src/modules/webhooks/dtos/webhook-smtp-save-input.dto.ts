import { Field, InputType } from '@nestjs/graphql';
import { IsBoolean, IsEmail, IsNotEmpty, IsN<PERSON>ber, IsString, ValidateNested } from 'class-validator';
import { validationMessagesLang, userValidationMessages } from '../../../languages/validation-messages.lang';
import { Type } from 'class-transformer';

@InputType()
export class WebhookSmtpConfigInputDto {
    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    host: string;

    @Field()
    @IsNumber()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    port: number;

    @Field()
    @IsBoolean()
    ssl: boolean;

    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    username: string;

    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    password: string;
}

@InputType()
export class WebhookSmtpSaveInputDto {
    @Field()
    @IsNumber()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    customer_id: number;

    @Field(() => WebhookSmtpConfigInputDto)
    @ValidateNested()
    @Type(() => WebhookSmtpConfigInputDto)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    smtp_config: WebhookSmtpConfigInputDto;
}

@InputType()
export class WebhookSmtpTestEmailInputDto {
    @Field(() => WebhookSmtpConfigInputDto)
    @ValidateNested()
    @Type(() => WebhookSmtpConfigInputDto)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    smtp_config: WebhookSmtpConfigInputDto;

    @Field()
    @IsEmail({}, { message: userValidationMessages.INVALID_EMAIL })
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    email: string;

    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    subject: string;

    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    content: string;
}
