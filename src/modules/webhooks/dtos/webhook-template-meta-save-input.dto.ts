import { Field, InputType, Int } from '@nestjs/graphql';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { TemplateEntity } from '../../../entities/template.entity';
import { UserEntity } from '../../../entities/user.entity';
import { IsNotEmpty, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';

@InputType()
export class WebhookTemplateMetaSaveInputDto {
    @Field(() => Int)
    @IdExists(TemplateEntity)
    template_id: number;

    @Field(() => Int)
    @IdExists(UserEntity)
    user_id: number;

    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    domain: string;

    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    page_slug: string;

    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    meta_key: string;

    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    meta_value: string;
}
