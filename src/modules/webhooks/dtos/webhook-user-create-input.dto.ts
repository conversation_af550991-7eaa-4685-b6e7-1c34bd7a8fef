import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { UserRoles } from '../../../entities/user.entity';

@InputType()
export class WebhookUserCreateInputDto {
    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    email: string;

    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    first_name: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    last_name?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    phone?: string;

    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    password: string;

    @Field(() => Int)
    @IsEnum(UserRoles)
    role_id: UserRoles;
}
