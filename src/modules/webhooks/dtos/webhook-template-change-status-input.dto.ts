import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { ItemStatus } from '../../../commons/enums.common';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { FileEntity } from '../../../entities/file.entity';

@InputType()
export class WebhookTemplateChangeStatusInputDto {
    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(ItemStatus)
    status_id: ItemStatus;

    @IsOptional()
    @IdExists(FileEntity)
    @Field(() => Int, { nullable: true })
    code_file_id?: number;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    reject_reason?: string;

    @IsOptional()
    @IdExists(FileEntity)
    @Field(() => Int, { nullable: true })
    reject_file_id?: number;
}
