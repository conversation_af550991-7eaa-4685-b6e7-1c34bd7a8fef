import { InputType, Field, ObjectType } from '@nestjs/graphql';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';
import { WebhookBuyDomainDto } from './webhook-buy-domain.dto';

@InputType()
export class WebhookCustomDomainDto {
    @IsOptional()
    @IsString()
    @Field()
    websiteId: string;

    @IsNotEmpty()
    @Field(() => WebhookBuyDomainDto)
    domainInfo: WebhookBuyDomainDto;
}

@InputType()
export class WebhookCustomDomainWithOtherProviderDto {
    @IsOptional()
    @IsString()
    @Field()
    websiteId: string;

    @IsOptional()
    @IsString()
    @Field()
    domain: string;
}

@ObjectType()
export class WebhookCustomDomainProcessResult {
    @Field({ nullable: true })
    ip?: string;

    @Field({ nullable: true })
    isSuccess: boolean;
}


@ObjectType()
export class WebhookCustomDomainProcessResultWithOtherProvider {
    @Field({ nullable: true })
    ip?: string;

    @Field({ nullable: true })
    isSuccess: boolean;

    @Field(() => [String], { nullable: true })
    nameServers?: string[];
}
