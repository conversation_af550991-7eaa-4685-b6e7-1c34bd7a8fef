import { InputType, Field } from '@nestjs/graphql';
import { IsNotEmpty, IsString, IsNumber, IsBoolean, IsEmail } from 'class-validator';

@InputType()
export class WebhookBuyDomainDto {
    @Field()
    @IsNotEmpty()
    @IsString()
    command: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    domainName: string;

    @Field()
    @IsNotEmpty()
    @IsNumber()
    years: number;

    @Field()
    @IsNotEmpty()
    @IsBoolean()
    addFreeWhoisguard: boolean;

    @Field()
    @IsNotEmpty()
    @IsBoolean()
    wgEnabled: boolean;

    @Field()
    @IsNotEmpty()
    @IsString()
    FirstName: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    LastName: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    Address1: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    City: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    Province: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    PostalCode: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    Country: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    Phone: string;

    @Field()
    @IsNotEmpty()
    @IsEmail()
    EmailAddress: string;
}
