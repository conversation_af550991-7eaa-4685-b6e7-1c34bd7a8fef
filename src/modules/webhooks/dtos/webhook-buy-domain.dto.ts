import { InputType, Field } from '@nestjs/graphql';
import { IsNotEmpty, IsString, IsNumber, IsBoolean, IsEmail } from 'class-validator';

@InputType()
export class WebhookBuyDomainDto {
    @Field()
    @IsNotEmpty()
    @IsString()
    command: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    domainName: string;

    @Field()
    @IsNotEmpty()
    @IsNumber()
    years: number;

    @Field()
    @IsNotEmpty()
    @IsBoolean()
    addFreeWhoisguard: boolean;

    @Field()
    @IsNotEmpty()
    @IsBoolean()
    wgEnabled: boolean;

    @Field()
    @IsNotEmpty()
    @IsString()
    registrantFirstName: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    registrantLastName: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    registrantAddress1: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    registrantCity: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    registrantStateProvince: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    registrantPostalCode: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    registrantCountry: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    registrantPhone: string;

    @Field()
    @IsNotEmpty()
    @IsEmail()
    registrantEmailAddress: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    techFirstName: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    techLastName: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    techAddress1: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    techCity: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    techStateProvince: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    techPostalCode: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    techCountry: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    techPhone: string;

    @Field()
    @IsNotEmpty()
    @IsEmail()
    techEmailAddress: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    adminFirstName: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    adminLastName: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    adminAddress1: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    adminCity: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    adminStateProvince: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    adminPostalCode: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    adminCountry: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    adminPhone: string;

    @Field()
    @IsNotEmpty()
    @IsEmail()
    adminEmailAddress: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    auxBillingFirstName: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    auxBillingLastName: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    auxBillingAddress1: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    auxBillingCity: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    auxBillingStateProvince: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    auxBillingPostalCode: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    auxBillingCountry: string;

    @Field()
    @IsNotEmpty()
    @IsString()
    auxBillingPhone: string;

    @Field()
    @IsNotEmpty()
    @IsEmail()
    auxBillingEmailAddress: string;
}
