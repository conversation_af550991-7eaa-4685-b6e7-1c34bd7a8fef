import { Args, Int, Mutation, Query, Resolver } from '@nestjs/graphql';
import { WebhookTokenGuard } from '../../../commons/guards/webhook-token.guard';
import { NotFoundException, UseGuards } from '@nestjs/common';
import { SmtpEntity } from '../../../entities/smtp.entity';
import { WebhookSmtpSaveInputDto, WebhookSmtpTestEmailInputDto } from '../dtos/webhook-smtp-save-input.dto';
import { SmtpService } from '../../stmp/stmp.service';
import * as nodemailer from 'nodemailer';

@Resolver('webhooks-smtp')
@UseGuards(WebhookTokenGuard)
export class WebhookSmtpResolver {
    constructor(private readonly smtpService: SmtpService) {}

    @Query(() => SmtpEntity, { name: 'webhooks_smtp_view_by_customer' })
    async viewByCustomer(@Args('customer_id', { type: () => Int }) customer_id: number): Promise<SmtpEntity> {
        const smtp = await this.smtpService.findOne({ where: { customer_id } });
        if (!smtp) throw new NotFoundException('SMTP configuration not found for this customer');
        return smtp;
    }

    @Mutation(() => SmtpEntity, { name: 'webhooks_smtp_save' })
    async save(@Args('input') input: WebhookSmtpSaveInputDto): Promise<SmtpEntity> {
        // Check if SMTP configuration already exists for this customer
        const existingSmtp = await this.smtpService.findOne({ where: { customer_id: input.customer_id } });

        if (existingSmtp) {
            // Update existing SMTP configuration
            return this.smtpService.updateOne(existingSmtp.id, {
                ...input,
                updated_by: undefined, // Webhook doesn't have user context
            });
        } else {
            // Create new SMTP configuration
            return this.smtpService.create({
                ...input,
                created_by: undefined, // Webhook doesn't have user context
            });
        }
    }

    @Mutation(() => Boolean, { name: 'webhooks_smtp_test_email' })
    async testEmail(@Args('input') input: WebhookSmtpTestEmailInputDto): Promise<boolean> {
        try {
            // Create a temporary mailer transport with the provided SMTP config
            const transporter = nodemailer.createTransport({
                host: input.smtp_config.host,
                port: input.smtp_config.port,
                secure: input.smtp_config.ssl, // true for 465, false for other ports
                auth: {
                    user: input.smtp_config.username,
                    pass: input.smtp_config.password,
                },
            });

            // Send test email
            // For SMTP services like SMTP2GO, use a simple noreply format or the username if it's already an email
            let fromEmail = input.smtp_config.username;

            // If username is not an email, use a generic noreply format
            if (!fromEmail.includes('@')) {
                fromEmail = '<EMAIL>';
            }

            await transporter.sendMail({
                from: fromEmail,
                to: input.email,
                subject: input.subject,
                html: input.content,
            });

            return true;
        } catch (error) {
            console.error('SMTP test email failed:', error);
            throw new Error(`Failed to send test email: ${error.message}`);
        }
    }

    @Mutation(() => Boolean, { name: 'webhooks_smtp_delete' })
    async delete(@Args('id', { type: () => Int }) id: number): Promise<boolean> {
        const smtp = await this.smtpService.findOne(id);
        if (!smtp) throw new NotFoundException('SMTP configuration not found');

        await this.smtpService.softDelete(id, undefined); // Webhook doesn't have user context
        return true;
    }
}
