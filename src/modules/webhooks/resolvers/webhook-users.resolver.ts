import { Args, Int, Mutation, Resolver } from '@nestjs/graphql';
import { WebhookUsersService } from '../services/webhook-users.service';
import { UserEntity } from '../../../entities/user.entity';
import { WebhookUserCreateInputDto } from '../dtos/webhook-user-create-input.dto';
import { WebhookUserUpdateInputDto } from '../dtos/webhook-user-update-input.dto';
import { WebhookUserUpdateInfoInputDto } from '../dtos/webhook-user-update-info-input.dto';
import { WebhookUserResetPassInputDto } from '../dtos/webhook-user-reset-pass-input.dto';
import { WebhookTokenGuard } from '../../../commons/guards/webhook-token.guard';
import { UseGuards } from '@nestjs/common';

@Resolver('webhooks-users')
@UseGuards(WebhookTokenGuard)
export class WebhookUsersResolver {
    constructor(private readonly webhookUsersService: WebhookUsersService) {}

    @Mutation(() => UserEntity, { name: 'webhooks_users_create' })
    async createUser(@Args('body') body: WebhookUserCreateInputDto): Promise<UserEntity> {
        return this.webhookUsersService.createUser(body);
    }

    @Mutation(() => UserEntity, { name: 'webhooks_users_update' })
    async updateUser(@Args('body') body: WebhookUserUpdateInputDto): Promise<UserEntity> {
        return this.webhookUsersService.updateUser(body);
    }

    @Mutation(() => UserEntity, { name: 'webhooks_users_update_info' })
    async updateUserInfo(
        @Args('id', { type: () => Int }) id: number,
        @Args('input') input: WebhookUserUpdateInfoInputDto
    ): Promise<UserEntity> {
        return this.webhookUsersService.updateUserInfo(id, input);
    }

    @Mutation(() => Boolean, { name: 'webhooks_users_reset_pass' })
    async resetUserPass(@Args('body') body: WebhookUserResetPassInputDto): Promise<Boolean> {
        await this.webhookUsersService.updateUser(body);
        return true;
    }
}
