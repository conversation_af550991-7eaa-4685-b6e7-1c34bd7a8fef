import { Args, Mutation, Resolver } from '@nestjs/graphql';
import { WebhookTokenGuard } from '../../../commons/guards/webhook-token.guard';
import { NotFoundException, UseGuards } from '@nestjs/common';
import { WebhookWebsiteSendEmailInputDto } from '../dtos/webhook-website-send-email-input.dto';
import { EmailService } from '../../e-mails/email.service';
import { WebsitesService } from '../../websites/websites.service';
import { SmtpService } from '../../stmp/stmp.service';
import * as nodemailer from 'nodemailer';

@Resolver('webhooks-website-send-email')
@UseGuards(WebhookTokenGuard)
export class WebhookWebsiteSendEmailResolver {
    constructor(
        private readonly emailService: EmailService,
        private readonly websitesService: WebsitesService,
        private readonly smtpService: SmtpService
    ) {}

    @Mutation(() => Boolean, { name: 'webhooks_website_send_email' })
    async sendEmail(@Args('input') input: WebhookWebsiteSendEmailInputDto): Promise<boolean> {
        // Verify website exists
        const website = await this.websitesService.findOne(input.website_id);
        if (!website) {
            throw new NotFoundException('Website not found');
        }

        // Prepare email content from fields
        const emailContent = this.formatEmailContent(input.fields);
        const subject = `Contact Form Submission from Website ${website.custom_domain ?? website.domain}`;

        // Check if customer has custom SMTP configuration
        const customerSmtp = await this.smtpService.findOne({ where: { customer_id: website.customer_id } });

        if (customerSmtp) {
            // Use customer's SMTP configuration
            try {
                const transporter = nodemailer.createTransport({
                    host: customerSmtp.smtp_config.host,
                    port: customerSmtp.smtp_config.port,
                    secure: customerSmtp.smtp_config.ssl,
                    auth: {
                        user: customerSmtp.smtp_config.username,
                        pass: customerSmtp.smtp_config.password,
                    },
                });

                // Use a proper email format for the from field
                let fromEmail = customerSmtp.smtp_config.username;

                // If username is not an email, use a generic noreply format
                if (!fromEmail.includes('@')) {
                    fromEmail = '<EMAIL>';
                }

                await transporter.sendMail({
                    from: fromEmail,
                    to: input.customer_email,
                    subject: subject,
                    html: emailContent,
                });
            } catch (error) {
                console.error('Customer SMTP failed, falling back to default:', error);
                // Fall back to default email service if customer SMTP fails
                await this.emailService.sendEmail({
                    to: input.customer_email,
                    subject: subject,
                    html: emailContent,
                });
            }
        } else {
            // Use default email service
            await this.emailService.sendEmail({
                to: input.customer_email,
                subject: subject,
                html: emailContent,
            });
        }

        return true;
    }

    private formatEmailContent(fields: { label: string; value: string }[]): string {
        let html = '<h2>Contact Form Submission</h2>';
        html += '<table style="border-collapse: collapse; width: 100%;">';

        fields.forEach((field) => {
            html += `
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold; background-color: #f2f2f2;">
                        ${this.escapeHtml(field.label)}:
                    </td>
                    <td style="border: 1px solid #ddd; padding: 8px;">
                        ${this.escapeHtml(field.value)}
                    </td>
                </tr>
            `;
        });

        html += '</table>';
        html += `<p><small>Sent at: ${new Date().toISOString()}</small></p>`;

        return html;
    }

    private escapeHtml(text: string): string {
        const map: { [key: string]: string } = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;',
        };
        return text.replace(/[&<>"']/g, (m) => map[m]);
    }
}
