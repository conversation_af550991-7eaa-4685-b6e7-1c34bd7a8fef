import { Args, Query, Resolver } from '@nestjs/graphql';
import { WebhookTokenGuard } from '../../../commons/guards/webhook-token.guard';
import { UseGuards } from '@nestjs/common';
import { MasterTagEntity } from '../../../entities/master-tag.entity';
import { MasterTagsService } from '../../master-tags/master-tags.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { MasterTagModel } from '../../master-tags/models/master-tag.model';

@Resolver('webhooks-master-tags')
@UseGuards(WebhookTokenGuard)
export class WebhookMasterTagsResolver {
    constructor(private readonly masterTagsService: MasterTagsService) {}

    @Query(() => MasterTagModel, { name: 'webhooks_master_tags_list' })
    async list(@Args('input') input: BasePaginationInput): Promise<IPaginatedType<MasterTagEntity>> {
        return this.masterTagsService.search(input);
    }

    @Query(() => [MasterTagEntity], { name: 'webhooks_master_tags_all' })
    async all(): Promise<MasterTagEntity[]> {
        return this.masterTagsService.findAllBy({});
    }
}
