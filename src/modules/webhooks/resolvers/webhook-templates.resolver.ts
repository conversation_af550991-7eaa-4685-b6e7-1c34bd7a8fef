import { Args, Int, Mutation, Resolver } from '@nestjs/graphql';
import { WebhookTokenGuard } from '../../../commons/guards/webhook-token.guard';
import { UseGuards } from '@nestjs/common';
import { WebhookTemplatesService } from '../services/webhook-templates.service';
import { WebhookTemplateChangeStatusInputDto } from '../dtos/webhook-template-change-status-input.dto';
import { TemplateEntity } from '../../../entities/template.entity';
import { WebhookTemplateSaveInputDto } from '../dtos/webhook-template-save-input.dto';

@Resolver('webhooks-templates')
@UseGuards(WebhookTokenGuard)
export class WebhookTemplatesResolver {
    constructor(private readonly webhookTemplatesService: WebhookTemplatesService) {}

    @Mutation(() => Boolean, { name: 'webhooks_templates_change_status' })
    async changeStatus(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: WebhookTemplateChangeStatusInputDto
    ): Promise<boolean> {
        await this.webhookTemplatesService.changeStatus(id, body);
        return true;
    }

    @Mutation(() => TemplateEntity, { name: 'webhooks_templates_template_create' })
    async create(@Args('body') body: WebhookTemplateSaveInputDto): Promise<TemplateEntity> {
        return this.webhookTemplatesService.saveTemplate(body);
    }

    @Mutation(() => TemplateEntity, { name: 'webhooks_templates_template_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: WebhookTemplateSaveInputDto
    ): Promise<TemplateEntity> {
        return this.webhookTemplatesService.saveTemplate(body, id);
    }
}
