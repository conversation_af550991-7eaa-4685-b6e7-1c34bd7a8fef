import { Args, Mutation } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { WebhookTokenGuard } from '../../../commons/guards/webhook-token.guard';
import { WebhookDomainService } from '../services/webhook-domain.service';
import {
    WebhookCustomDomainDto,
    WebhookCustomDomainWithOtherProviderDto,
    WebhookCustomDomainProcessResult,
    WebhookCustomDomainProcessResultWithOtherProvider,
} from '../dtos/webhook-custom-domain.dto';

@UseGuards(WebhookTokenGuard)
export class WebhookCustomDomainResolver {
    constructor(private readonly webhookDomainService: WebhookDomainService) {}

    @Mutation(() => WebhookCustomDomainProcessResult, { name: 'webhooks_custom_domain_process' })
    async process_domain(@Args('body') body: WebhookCustomDomainDto): Promise<WebhookCustomDomainProcessResult> {
        return this.webhookDomainService.processDomain(body);
    }

    @Mutation(() => WebhookCustomDomainProcessResultWithOtherProvider, { name: 'webhooks_custom_domain_process_with_other_provider' })
    async process_domain_with_other_provider(
        @Args('body') body: WebhookCustomDomainWithOtherProviderDto
    ): Promise<WebhookCustomDomainProcessResultWithOtherProvider> {
        return this.webhookDomainService.processDomainWithOtherProvider(body);
    }
}
