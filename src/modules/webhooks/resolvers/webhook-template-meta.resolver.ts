import { Args, Mutation, Resolver } from '@nestjs/graphql';
import { WebhookTokenGuard } from '../../../commons/guards/webhook-token.guard';
import { UseGuards } from '@nestjs/common';
import { WebhookTemplateMetaSaveInputDto } from '../dtos/webhook-template-meta-save-input.dto';
import { TemplateMetaEntity } from '../../../entities/template-meta.entity';
import { WebhookTemplateMetaService } from '../services/webhook-template-meta.service';

@Resolver('webhooks-template-meta')
@UseGuards(WebhookTokenGuard)
export class WebhookTemplateMetaResolver {
    constructor(private readonly webhookTemplateMetaService: WebhookTemplateMetaService) {}

    @Mutation(() => TemplateMetaEntity, { name: 'webhooks_templates_meta_save' })
    async save(@Args('body') body: WebhookTemplateMetaSaveInputDto): Promise<TemplateMetaEntity> {
        return this.webhookTemplateMetaService.saveTemplateMeta(body);
    }
}
