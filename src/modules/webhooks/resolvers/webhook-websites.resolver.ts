import { Args, Int, Mutation, Resolver } from '@nestjs/graphql';
import { WebhookTokenGuard } from '../../../commons/guards/webhook-token.guard';
import { BadRequestException, NotFoundException, UseGuards } from '@nestjs/common';
import { CheckCronTemplateInputDto } from '../../cron-templates/dtos/check-cron-template-input.dto';
import { CronTemplatesService } from '../../cron-templates/cron-templates.service';
import { WebsiteEntity } from '../../../entities/website.entity';
import { WebhookWebsiteUpdateCustomDomainInputDto } from '../dtos/webhook-website-update-custom-domain-input.dto';

@Resolver('webhooks-websites')
@UseGuards(WebhookTokenGuard)
export class WebhookWebsitesResolver {
    constructor(private readonly cronTemplatesService: CronTemplatesService) {}

    @Mutation(() => Boolean, { name: 'webhooks_websites_deploy_by_id' })
    async deployById(@Args('id', { type: () => Int }) id: number): Promise<boolean> {
        const website = await this.cronTemplatesService.repo.manager.getRepository(WebsiteEntity).findOneBy({ id });
        if (!website) throw new NotFoundException();
        if (!website.domain) throw new BadRequestException();
        const body: CheckCronTemplateInputDto = {
            template_id: website.template_id,
            domain: website.domain,
        };
        return this.cronTemplatesService.checkExits(body);
    }

    @Mutation(() => WebsiteEntity, { name: 'webhooks_websites_update_custom_domain' })
    async updateCustomDomain(
        @Args('id', { type: () => Int }) id: number,
        @Args('input') input: WebhookWebsiteUpdateCustomDomainInputDto
    ): Promise<WebsiteEntity> {
        const websiteRepository = this.cronTemplatesService.repo.manager.getRepository(WebsiteEntity);

        const website = await websiteRepository.findOneBy({ id });
        if (!website) throw new NotFoundException('Website not found');

        await websiteRepository.update(id, {
            custom_domain: input.custom_domain,
            updated_at: new Date(),
        });

        const updatedWebsite = await websiteRepository.findOneBy({ id });
        return updatedWebsite!;
    }
}
