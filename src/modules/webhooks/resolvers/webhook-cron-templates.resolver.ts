import { Args, Query, Resolver } from '@nestjs/graphql';
import { WebhookTokenGuard } from '../../../commons/guards/webhook-token.guard';
import { UseGuards } from '@nestjs/common';
import { CheckCronTemplateInputDto } from '../../cron-templates/dtos/check-cron-template-input.dto';
import { CronTemplatesService } from '../../cron-templates/cron-templates.service';

@Resolver('webhooks-cron-templates')
@UseGuards(WebhookTokenGuard)
export class WebhookCronTemplatesResolver {
    constructor(private readonly cronTemplatesService: CronTemplatesService) {}

    @Query(() => Boolean, { name: 'webhooks_template_check_exits' })
    async checkExits(@Args('body') body: CheckCronTemplateInputDto): Promise<boolean> {
        return this.cronTemplatesService.checkExits(body);
    }
}
