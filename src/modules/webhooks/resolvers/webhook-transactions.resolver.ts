import { Args, Mu<PERSON>, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { WebhookTokenGuard } from '../../../commons/guards/webhook-token.guard';
import { UseGuards } from '@nestjs/common';
import { WebhookTransactionsService } from '../services/webhook-transactions.service';
import { TransactionSaveInputDto } from '../../transactions/dtos/transaction-save-input.dto';
import { TransactionEntity } from '../../../entities/transaction.entity';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { TransactionModel } from '../../transactions/models/transaction.model';
import { SubscriptionPlanEntity } from '../../../entities/subscription-plan.entity';
import { WebsiteEntity } from '../../../entities/website.entity';
import { UserEntity } from '../../../entities/user.entity';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';

@Resolver(() => TransactionEntity)
@UseGuards(WebhookTokenGuard)
export class WebhookTransactionsResolver {
    constructor(
        private readonly webhookTransactionsService: WebhookTransactionsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => SubscriptionPlanEntity, { nullable: true })
    async plan(@Parent() transaction: TransactionEntity): Promise<SubscriptionPlanEntity | null> {
        if (!transaction.plan_id) return null;
        return this.dataLoader.relationBatchOne(SubscriptionPlanEntity).load(transaction.plan_id);
    }

    @ResolveField(() => WebsiteEntity, { nullable: true })
    async website(@Parent() transaction: TransactionEntity): Promise<WebsiteEntity | null> {
        if (!transaction.website_id) return null;
        return this.dataLoader.relationBatchOne(WebsiteEntity).load(transaction.website_id);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async customer(@Parent() transaction: TransactionEntity): Promise<UserEntity | null> {
        if (!transaction.customer_id) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(transaction.customer_id);
    }

    @Query(() => TransactionModel, { name: 'webhooks_transactions_list' })
    async list(@Args('input') input: BasePaginationInput): Promise<IPaginatedType<TransactionEntity>> {
        return this.webhookTransactionsService.search(input);
    }

    @Mutation(() => TransactionEntity, { name: 'webhooks_transactions_create' })
    async create(@Args('input') input: TransactionSaveInputDto): Promise<TransactionEntity> {
        return this.webhookTransactionsService.create(input);
    }
}
