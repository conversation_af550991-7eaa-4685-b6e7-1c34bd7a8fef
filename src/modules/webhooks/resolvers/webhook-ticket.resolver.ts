import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { TicketEntity } from '../../../entities/ticket.entity';
import { TicketsService } from '../../tickets/tickets.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { TicketModel } from '../../tickets/models/ticket.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { WebhookTokenGuard } from '../../../commons/guards/webhook-token.guard';
import { TicketCreateInputDto } from '../../tickets/dtos/ticket-create-input.dto';
import { TicketStatus } from '../../../entities/ticket.entity';

@Resolver(() => TicketEntity)
@UseGuards(WebhookTokenGuard)
export class WebhookTicketResolver {
    constructor(private readonly ticketsService: TicketsService) {}

    @Query(() => TicketModel, { name: 'webhooks_tickets_list' })
    async webhookTicketsList(@Args('input') input: BasePaginationInput): Promise<IPaginatedType<TicketEntity>> {
        return this.ticketsService.search(input);
    }

    @Mutation(() => TicketEntity, { name: 'webhooks_tickets_create' })
    async webhookCreateTicket(@Args('input') input: TicketCreateInputDto): Promise<TicketEntity> {
        return this.ticketsService.create({
            ...input,
            status_id: TicketStatus.PENDING,
            created_by: input.customer_id,
        });
    }
}
