import { Args, Int, Mutation, Query, Resolver } from '@nestjs/graphql';
import { WebhookTokenGuard } from '../../../commons/guards/webhook-token.guard';
import { NotFoundException, UseGuards } from '@nestjs/common';
import { WebhookSubscriptionPlansService } from '../services/webhook-subscription-plans.service';
import { BaseInput } from '../../../commons/bases/base.input';
import { SubscriptionPlanEntity } from '../../../entities/subscription-plan.entity';
import { SubscriptionPlanSaveInputDto } from '../../subscriptions/dtos/subscription-plan-save-input.dto';

@Resolver('webhooks-subscription-plans')
@UseGuards(WebhookTokenGuard)
export class WebhookSubscriptionPlansResolver {
    constructor(private readonly webhookSubscriptionPlansService: WebhookSubscriptionPlansService) {}

    @Query(() => [SubscriptionPlanEntity], { name: 'webhooks_subscription_plan_list' })
    async index(@Args('body') body: BaseInput): Promise<SubscriptionPlanEntity[]> {
        return this.webhookSubscriptionPlansService.findAllBy(body);
    }

    @Query(() => SubscriptionPlanEntity, { name: 'webhooks_subscription_plan_view' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<SubscriptionPlanEntity> {
        return this.webhookSubscriptionPlansService.findOne(id).then((sp) => {
            if (!sp) throw new NotFoundException();
            return sp;
        });
    }

    @Mutation(() => SubscriptionPlanEntity, { name: 'webhooks_subscription_plan_create' })
    async store(@Args('body') body: SubscriptionPlanSaveInputDto): Promise<SubscriptionPlanEntity> {
        return this.webhookSubscriptionPlansService.create(body);
    }

    @Mutation(() => SubscriptionPlanEntity, { name: 'webhooks_subscription_plan_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: SubscriptionPlanSaveInputDto
    ): Promise<SubscriptionPlanEntity> {
        return this.webhookSubscriptionPlansService.updateOne(id, body);
    }

    @Mutation(() => Boolean, { name: 'webhooks_subscription_plan_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number): Promise<Boolean> {
        await this.webhookSubscriptionPlansService.softDelete(id);
        return true;
    }
}
