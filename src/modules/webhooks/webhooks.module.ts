import { Module } from '@nestjs/common';
import { WebhookUsersResolver } from './resolvers/webhook-users.resolver';
import { WebhookUsersService } from './services/webhook-users.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserEntity } from '../../entities/user.entity';
import { WebhookSubscriptionPlansResolver } from './resolvers/webhook-subscription-plans.resolver';
import { WebhookSubscriptionPlansService } from './services/webhook-subscription-plans.service';
import { WebhookTransactionsResolver } from './resolvers/webhook-transactions.resolver';
import { WebhookTransactionsService } from './services/webhook-transactions.service';
import { TransactionEntity } from '../../entities/transaction.entity';
import { SubscriptionPlanEntity } from '../../entities/subscription-plan.entity';
import { WebhookTemplatesResolver } from './resolvers/webhook-templates.resolver';
import { WebhookTemplatesService } from './services/webhook-templates.service';
import { TemplateEntity } from '../../entities/template.entity';
import { TemplatesModule } from '../templates/templates.module';
import { WebhookTemplateMetaResolver } from './resolvers/webhook-template-meta.resolver';
import { WebhookTemplateMetaService } from './services/webhook-template-meta.service';
import { TemplateMetaEntity } from '../../entities/template-meta.entity';
import { CronTemplatesModule } from '../cron-templates/cron-templates.module';
import { WebhookCronTemplatesResolver } from './resolvers/webhook-cron-templates.resolver';
import { CronTemplateEntity } from '../../entities/cron-template.entity';
import { WebhookWebsitesResolver } from './resolvers/webhook-websites.resolver';
import { WebhookSmtpResolver } from './resolvers/webhook-smtp.resolver';
import { WebhookWebsiteSendEmailResolver } from './resolvers/webhook-website-send-email.resolver';
import { WebhookMasterTagsResolver } from './resolvers/webhook-master-tags.resolver';
import { WebhookTicketResolver } from './resolvers/webhook-ticket.resolver';
import { WebhookCustomDomainResolver } from './resolvers/webhook-custom-domain.resolver';
import { SmtpModule } from '../stmp/stmp.module';
import { WebsitesModule } from '../websites/websites.module';
import { EmailModule } from '../e-mails/email.module';
import { DataLoaderModule } from '../../data-loaders/data-loaders.module';
import { MasterTagsModule } from '../master-tags/master-tags.module';
import { TicketsModule } from '../tickets/tickets.module';
import { WebhookDomainService } from './services/webhook-domain.service';
import { WebsiteEntity } from '../../entities/website.entity';
import { AxiosModule } from '../../axios/axios.module';

@Module({
    providers: [
        WebhookUsersResolver,
        WebhookUsersService,
        WebhookSubscriptionPlansResolver,
        WebhookSubscriptionPlansService,
        WebhookTransactionsResolver,
        WebhookTransactionsService,
        WebhookTemplatesResolver,
        WebhookTemplatesService,
        WebhookTemplateMetaResolver,
        WebhookTemplateMetaService,
        WebhookCronTemplatesResolver,
        WebhookWebsitesResolver,
        WebhookSmtpResolver,
        WebhookWebsiteSendEmailResolver,
        WebhookMasterTagsResolver,
        WebhookTicketResolver,
        WebhookCustomDomainResolver,
        WebhookDomainService,
    ],
    imports: [
        TypeOrmModule.forFeature([
            UserEntity,
            TransactionEntity,
            SubscriptionPlanEntity,
            TemplateEntity,
            TemplateMetaEntity,
            CronTemplateEntity,
            WebsiteEntity,
        ]),
        TemplatesModule,
        CronTemplatesModule,
        SmtpModule,
        WebsitesModule,
        EmailModule,
        DataLoaderModule,
        MasterTagsModule,
        TicketsModule,
        AxiosModule,
    ],
})
export class WebhooksModule {}
