import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SubscriptionPlanEntity } from '../../../entities/subscription-plan.entity';

@Injectable()
export class WebhookSubscriptionPlansService extends BaseService<SubscriptionPlanEntity> {
    constructor(@InjectRepository(SubscriptionPlanEntity) public readonly repo: Repository<SubscriptionPlanEntity>) {
        super(repo);
    }
}
