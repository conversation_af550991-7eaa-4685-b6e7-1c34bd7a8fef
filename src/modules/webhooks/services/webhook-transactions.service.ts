import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TransactionEntity } from '../../../entities/transaction.entity';

@Injectable()
export class WebhookTransactionsService extends BaseService<TransactionEntity> {
    constructor(@InjectRepository(TransactionEntity) public readonly repo: Repository<TransactionEntity>) {
        super(repo);
    }
}
