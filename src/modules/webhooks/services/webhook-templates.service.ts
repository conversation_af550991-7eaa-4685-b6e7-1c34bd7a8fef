import { ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TemplateEntity } from '../../../entities/template.entity';
import { TemplatesService } from '../../templates/services/templates.service';
import { WebhookTemplateSaveInputDto } from '../dtos/webhook-template-save-input.dto';
import { WebhookTemplateChangeStatusInputDto } from '../dtos/webhook-template-change-status-input.dto';

@Injectable()
export class WebhookTemplatesService extends BaseService<TemplateEntity> {
    constructor(
        private readonly templatesService: TemplatesService,
        @InjectRepository(TemplateEntity) public readonly repo: Repository<TemplateEntity>
    ) {
        super(repo);
    }

    async changeStatus(id: number, body: WebhookTemplateChangeStatusInputDto): Promise<void> {
        const template = await this.findOne(id);
        if (!template) throw new NotFoundException();
        if (template.is_kit) throw new ForbiddenException();
        await this.repo.update(id, body);
    }

    async saveTemplate(body: WebhookTemplateSaveInputDto, id?: number): Promise<TemplateEntity> {
        return this.templatesService.saveTemplate({ ...body, is_kit: false }, id);
    }
}
