import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TemplateMetaEntity } from '../../../entities/template-meta.entity';
import { WebhookTemplateMetaSaveInputDto } from '../dtos/webhook-template-meta-save-input.dto';

@Injectable()
export class WebhookTemplateMetaService extends BaseService<TemplateMetaEntity> {
    constructor(@InjectRepository(TemplateMetaEntity) public readonly repo: Repository<TemplateMetaEntity>) {
        super(repo);
    }

    async saveTemplateMeta(body: WebhookTemplateMetaSaveInputDto): Promise<TemplateMetaEntity> {
        const existingMeta = await this.findOne({
            where: {
                template_id: body.template_id,
                user_id: body.user_id,
                domain: body.domain,
                page_slug: body.page_slug,
                meta_key: body.meta_key,
            },
        });
        if (existingMeta) {
            return this.updateOne(existingMeta.id, body);
        }
        return this.create(body);
    }
}
