import { Injectable, NotFoundException } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { UserEntity, UserStatus } from '../../../entities/user.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { WebhookUserCreateInputDto } from '../dtos/webhook-user-create-input.dto';
import { WebhookUserUpdateInputDto } from '../dtos/webhook-user-update-input.dto';
import { WebhookUserUpdateInfoInputDto } from '../dtos/webhook-user-update-info-input.dto';
import { genPassword } from '../../../commons/helpers/hash-code.helper';

@Injectable()
export class WebhookUsersService extends BaseService<UserEntity> {
    constructor(@InjectRepository(UserEntity) public readonly repo: Repository<UserEntity>) {
        super(repo);
    }

    async createUser(body: WebhookUserCreateInputDto): Promise<UserEntity> {
        const hashedPassword = await genPassword(body.password);
        const newUser = this.repo.create({
            ...body,
            password: hashedPassword,
            status_id: UserStatus.ACTIVE,
        });
        return this.save(newUser);
    }

    async updateUser(body: WebhookUserUpdateInputDto): Promise<UserEntity> {
        let user = await this.findOne({ where: { email: body.email } });
        if (!user) {
            throw new NotFoundException();
        }
        const cleanedData = Object.fromEntries(
            Object.entries(body).filter(([_, value]) => value !== undefined)
        ) as QueryDeepPartialEntity<UserEntity>;
        if (cleanedData.password) cleanedData.password = await genPassword(cleanedData.password.toString());
        Object.assign(user, cleanedData);
        return this.save(user);
    }

    async updateUserInfo(id: number, body: WebhookUserUpdateInfoInputDto): Promise<UserEntity> {
        let user = await this.findOne({ where: { id } });
        if (!user) {
            throw new NotFoundException('User not found');
        }
        user.info = body.info;
        return this.save(user);
    }
}
