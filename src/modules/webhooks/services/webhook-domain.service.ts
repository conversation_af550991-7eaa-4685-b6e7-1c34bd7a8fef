import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WebhookCustomDomainDto, WebhookCustomDomainWithOtherProviderDto } from '../dtos/webhook-custom-domain.dto';
import { WebsiteEntity } from '../../../entities/website.entity';
import { CronTemplateEntity } from '../../../entities/cron-template.entity';
import { CloudflareAxios } from '../../../axios/cloudflare.axios';
import { NamecheapAxios } from '../../../axios/namecheap.axios';
import { random } from 'lodash';

@Injectable()
export class WebhookDomainService {
    constructor(
        @InjectRepository(WebsiteEntity)
        private readonly websiteRepository: Repository<WebsiteEntity>,
        @InjectRepository(CronTemplateEntity)
        private readonly cronTemplateRepository: Repository<CronTemplateEntity>,
        private readonly cloudflareAxios: CloudflareAxios,
        private readonly namecheapAxios: NamecheapAxios
    ) {}

    async getIpFromWebsiteId(websiteId: string): Promise<string> {
        const website = await this.websiteRepository.findOne({
            where: { id: parseInt(websiteId!) },
        });

        if (!website) {
            throw new Error(`Website with id ${websiteId} not found`);
        }

        if (!website.domain) {
            throw new Error(`Domain not found for website with id ${websiteId}`);
        }

        const cronTemplate = await this.cronTemplateRepository.findOne({
            where: { domain: website.domain },
        });

        if (!cronTemplate) {
            throw new Error(`Deployment not found on ${website.domain}`);
        }

        let ip: string | undefined = undefined;
        if (cronTemplate && cronTemplate.deploy_status && cronTemplate.deploy_status.ip) {
            ip = cronTemplate.deploy_status.ip;
        }

        if (!ip) {
            throw new Error(`IP not found in deploy_status for domain ${website.domain}`);
        }

        return ip;
    }

    async processDomain(body: WebhookCustomDomainDto) {
        const ip = await this.getIpFromWebsiteId(body.websiteId!);

        let zone;
        try {
            const existingZone = await this.cloudflareAxios.getZoneByName(body.domainInfo.domainName);

            if (existingZone.result.status === 'active') {
                throw new BadRequestException(
                    `Zone for domain ${body.domainInfo.domainName} is already active on Cloudflare. Contact support to modify the zone.`
                );
            }

            zone = {
                name_servers: existingZone.result.name_servers,
            };
            console.log(`Zone already exists for domain: ${body.domainInfo.domainName}`);
        } catch (error) {
            if (error.status === 404) {
                console.log(`Zone not found, creating new zone for domain: ${body.domainInfo.domainName}`);
                zone = await this.cloudflareAxios.createZone({ name: body.domainInfo.domainName });
            } else {
                throw error;
            }
        }

        await this.createCustomDomain(body, zone.name_servers);

        await this.cloudflareAxios.createDnsRecord({
            type: 'A',
            name: body.domainInfo.domainName,
            content: ip,
            ttl: 3600, // 1 hour
            proxied: false,
            domain: body.domainInfo.domainName,
        });

        await this.cloudflareAxios.createDnsRecord({
            type: 'CNAME',
            name: `www.${body.domainInfo.domainName}`,
            content: body.domainInfo.domainName,
            ttl: 3600,
            proxied: false,
            domain: body.domainInfo.domainName,
        });

        await this.updateWebsiteCustomDomain(body.websiteId!, body.domainInfo.domainName);

        return {
            ip,
            isSuccess: true,
        };
    }

    async processDomainWithOtherProvider(body: WebhookCustomDomainWithOtherProviderDto) {
        const ip = await this.getIpFromWebsiteId(body.websiteId!);

        let zone;
        try {
            const existingZone = await this.cloudflareAxios.getZoneByName(body.domain);
            zone = {
                name_servers: existingZone.result.name_servers,
            };
            if (existingZone.result.status === 'active') {
                throw new BadRequestException(
                    `Zone for domain ${body.domain} is already active on Cloudflare. Contact support to modify the zone.`
                );
            }
        } catch (error) {
            if (error.status === 404) {
                const newZone = await this.cloudflareAxios.createZone({ name: body.domain });
                zone = {
                    name_servers: newZone.name_servers,
                };
            } else {
                throw error;
            }
        }

        await this.cloudflareAxios.createDnsRecord({
            type: 'A',
            name: body.domain,
            content: ip,
            ttl: 3600, // 1 hour
            proxied: false,
            domain: body.domain,
        });

        await this.cloudflareAxios.createDnsRecord({
            type: 'CNAME',
            name: `www.${body.domain}`,
            content: body.domain,
            ttl: 3600,
            proxied: false,
            domain: body.domain,
        });

        await this.updateWebsiteCustomDomain(body.websiteId!, body.domain);

        return {
            ip,
            isSuccess: true,
            nameServers: zone.name_servers,
        };
    }

    async createCustomDomain(body: WebhookCustomDomainDto, customNameServers?: string[]) {
        const nameServers = customNameServers;
        const params = new URLSearchParams();
        const info = body.domainInfo;
        params.append('Command', info.command);
        params.append('DomainName', info.domainName);
        params.append('Years', info.years.toString());
        params.append('AddFreeWhoisguard', info.addFreeWhoisguard ? 'yes' : 'no');
        params.append('WGEnabled', info.wgEnabled ? 'yes' : 'no');
        // Registrant (using the same info for all contact types as per Namecheap requirements)
        params.append('RegistrantFirstName', info.FirstName);
        params.append('RegistrantLastName', info.LastName);
        params.append('RegistrantAddress1', info.Address1);
        params.append('RegistrantCity', info.City);
        params.append('RegistrantStateProvince', info.Province);
        params.append('RegistrantPostalCode', info.PostalCode);
        params.append('RegistrantCountry', info.Country);
        params.append('RegistrantPhone', info.Phone);
        params.append('RegistrantEmailAddress', info.EmailAddress);

        // Tech (using same info as registrant)
        params.append('TechFirstName', info.FirstName);
        params.append('TechLastName', info.LastName);
        params.append('TechAddress1', info.Address1);
        params.append('TechCity', info.City);
        params.append('TechStateProvince', info.Province);
        params.append('TechPostalCode', info.PostalCode);
        params.append('TechCountry', info.Country);
        params.append('TechPhone', info.Phone);
        params.append('TechEmailAddress', info.EmailAddress);

        // Admin (using same info as registrant)
        params.append('AdminFirstName', info.FirstName);
        params.append('AdminLastName', info.LastName);
        params.append('AdminAddress1', info.Address1);
        params.append('AdminCity', info.City);
        params.append('AdminStateProvince', info.Province);
        params.append('AdminPostalCode', info.PostalCode);
        params.append('AdminCountry', info.Country);
        params.append('AdminPhone', info.Phone);
        params.append('AdminEmailAddress', info.EmailAddress);

        // AuxBilling (using same info as registrant)
        params.append('AuxBillingFirstName', info.FirstName);
        params.append('AuxBillingLastName', info.LastName);
        params.append('AuxBillingAddress1', info.Address1);
        params.append('AuxBillingCity', info.City);
        params.append('AuxBillingStateProvince', info.Province);
        params.append('AuxBillingPostalCode', info.PostalCode);
        params.append('AuxBillingCountry', info.Country);
        params.append('AuxBillingPhone', info.Phone);
        params.append('AuxBillingEmailAddress', info.EmailAddress);

        if (nameServers && nameServers.length > 0) {
            params.append('Nameservers', nameServers.join(','));
        }
        const response = await this.namecheapAxios.exect(params.toString());

        if (response.ApiResponse['$'].Status === 'ERROR') {
            const errorMessage =
                response.ApiResponse.Errors?.[0]?.Error?.[0]?._ ||
                response.ApiResponse.Errors?.[0]?._ ||
                'Domain purchase failed';
            throw new BadRequestException({
                message: errorMessage,
                errors: response.ApiResponse.Errors,
            });
        }

        return response;
    }

    async updateWebsiteCustomDomain(websiteId: string, domain: string) {
        const website = await this.websiteRepository.findOne({
            where: { id: parseInt(websiteId) },
        });
        if (!website) {
            throw new BadRequestException(`Website with id ${websiteId} not found`);
        }
        website.custom_domain = domain;
        await this.websiteRepository.save(website);
    }
}
