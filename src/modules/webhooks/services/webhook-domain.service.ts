import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WebhookCustomDomainDto, WebhookCustomDomainWithOtherProviderDto } from '../dtos/webhook-custom-domain.dto';
import { WebsiteEntity } from '../../../entities/website.entity';
import { CronTemplateEntity } from '../../../entities/cron-template.entity';
import { CloudflareAxios } from '../../../axios/cloudflare.axios';
import { NamecheapAxios } from '../../../axios/namecheap.axios';
import { random } from 'lodash';

@Injectable()
export class WebhookDomainService {
    constructor(
        @InjectRepository(WebsiteEntity)
        private readonly websiteRepository: Repository<WebsiteEntity>,
        @InjectRepository(CronTemplateEntity)
        private readonly cronTemplateRepository: Repository<CronTemplateEntity>,
        private readonly cloudflareAxios: CloudflareAxios,
        private readonly namecheapAxios: NamecheapAxios
    ) {}


    async getIpFromWebsiteId(websiteId: string): Promise<string> {
        const website = await this.websiteRepository.findOne({
            where: { id: parseInt(websiteId!) },
        });

        if (!website) {
            throw new Error(`Website with id ${websiteId} not found`);
        }

        if (!website.domain) {
            throw new Error(`Domain not found for website with id ${websiteId}`);
        }

        const cronTemplate = await this.cronTemplateRepository.findOne({
            where: { domain: website.domain },
        });

        if (!cronTemplate) {
            throw new Error(`Deployment not found on ${website.domain}`);
        }

        let ip: string | undefined = undefined;
        if (cronTemplate && cronTemplate.deploy_status && cronTemplate.deploy_status.ip) {
            ip = cronTemplate.deploy_status.ip;
        }

        if (!ip) {
            throw new Error(`IP not found in deploy_status for domain ${website.domain}`);
        }

        return ip;
    }

    async processDomain(body: WebhookCustomDomainDto) {
        const ip = await this.getIpFromWebsiteId(body.websiteId!);

        const zone = await this.cloudflareAxios.createZone({ name: body.domainInfo.domainName });

        await this.createCustomDomain(body, zone.name_servers);

        await this.cloudflareAxios.createDnsRecord({
            type: 'A',
            name: body.domainInfo.domainName,
            content: ip,
            ttl: 3600, // 1 hour
            proxied: false,
            domain: body.domainInfo.domainName,
        });

        await this.cloudflareAxios.createDnsRecord({
            type: 'CNAME',
            name: `www.${body.domainInfo.domainName}`,
            content: body.domainInfo.domainName,
            ttl: 3600,
            proxied: false,
            domain: body.domainInfo.domainName,
        });

        await this.updateWebsiteCustomDomain(body.websiteId!,body.domainInfo.domainName);

        return {
            ip,
            isSuccess: true,
        };
    }

    async processDomainWithOtherProvider(body: WebhookCustomDomainWithOtherProviderDto) {
        const ip = await this.getIpFromWebsiteId(body.websiteId!);

        await this.cloudflareAxios.createDnsRecord({
            type: 'A',
            name: body.domain,
            content: ip,
            ttl: 3600, // 1 hour
            proxied: false,
            domain: body.domain,
        });

        await this.cloudflareAxios.createDnsRecord({
            type: 'CNAME',
            name: `www.${body.domain}`,
            content: body.domain,
            ttl: 3600,
            proxied: false,
            domain: body.domain,
        });

        await this.updateWebsiteCustomDomain(body.websiteId!, body.domain);

        return {
            ip,
            isSuccess: true,
        };
    }



    async createCustomDomain(body: WebhookCustomDomainDto, customNameServers?: string[]) {
        const nameServers = customNameServers;
        const params = new URLSearchParams();
        const info = body.domainInfo;
        params.append('Command', info.command);
        params.append('DomainName', info.domainName);
        params.append('Years', info.years.toString());
        params.append('AddFreeWhoisguard', info.addFreeWhoisguard ? 'yes' : 'no');
        params.append('WGEnabled', info.wgEnabled ? 'yes' : 'no');
        // Registrant
        params.append('RegistrantFirstName', info.registrantFirstName);
        params.append('RegistrantLastName', info.registrantLastName);
        params.append('RegistrantAddress1', info.registrantAddress1);
        params.append('RegistrantCity', info.registrantCity);
        params.append('RegistrantStateProvince', info.registrantStateProvince);
        params.append('RegistrantPostalCode', info.registrantPostalCode);
        params.append('RegistrantCountry', info.registrantCountry);
        params.append('RegistrantPhone', info.registrantPhone);
        params.append('RegistrantEmailAddress', info.registrantEmailAddress);
        params.append('TechFirstName', info.techFirstName);
        params.append('TechLastName', info.techLastName);
        params.append('TechAddress1', info.techAddress1);
        params.append('TechCity', info.techCity);
        params.append('TechStateProvince', info.techStateProvince);
        params.append('TechPostalCode', info.techPostalCode);
        params.append('TechCountry', info.techCountry);
        params.append('TechPhone', info.techPhone);
        params.append('TechEmailAddress', info.techEmailAddress);
        params.append('AdminFirstName', info.adminFirstName);
        params.append('AdminLastName', info.adminLastName);
        params.append('AdminAddress1', info.adminAddress1);
        params.append('AdminCity', info.adminCity);
        params.append('AdminStateProvince', info.adminStateProvince);
        params.append('AdminPostalCode', info.adminPostalCode);
        params.append('AdminCountry', info.adminCountry);
        params.append('AdminPhone', info.adminPhone);
        params.append('AdminEmailAddress', info.adminEmailAddress);
        params.append('AuxBillingFirstName', info.auxBillingFirstName);
        params.append('AuxBillingLastName', info.auxBillingLastName);
        params.append('AuxBillingAddress1', info.auxBillingAddress1);
        params.append('AuxBillingCity', info.auxBillingCity);
        params.append('AuxBillingStateProvince', info.auxBillingStateProvince);
        params.append('AuxBillingPostalCode', info.auxBillingPostalCode);
        params.append('AuxBillingCountry', info.auxBillingCountry);
        params.append('AuxBillingPhone', info.auxBillingPhone);
        params.append('AuxBillingEmailAddress', info.auxBillingEmailAddress);

        if (nameServers && nameServers.length > 0) {
            params.append('Nameservers', nameServers.join(','));
        }
        const response = await this.namecheapAxios.exect(params.toString());

        if (response.ApiResponse["$"].Status === "ERROR") {
            const errorMessage = response.ApiResponse.Errors?.[0]?.Error?.[0]?._ ||
                response.ApiResponse.Errors?.[0]?._ ||
                'Domain purchase failed';
            throw new BadRequestException({
                message: errorMessage,
                errors: response.ApiResponse.Errors
            });
        }

        return response
    }


    async updateWebsiteCustomDomain(websiteId: string, domain: string) {
        const website = await this.websiteRepository.findOne({
            where: { id: parseInt(websiteId) },
        });
        if (!website) {
            throw new BadRequestException(`Website with id ${websiteId} not found`);
        }
        website.custom_domain = domain;
        await this.websiteRepository.save(website);
    }

}

