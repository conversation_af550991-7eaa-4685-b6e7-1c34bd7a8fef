import { Field, InputType, Int, IntersectionType } from '@nestjs/graphql';
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { UserRoles, UserStatus } from '../../../entities/user.entity';
import { ProfileInputDto } from '../../auth/dtos/profile-input.dto';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';

@InputType()
export class UserUpdateInputDto extends IntersectionType(ProfileInputDto, BaseUpdateInputDto) {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(UserStatus)
    status_id?: UserStatus;

    @Field(() => Int, { nullable: true })
    @IsEnum(UserRoles)
    @IsOptional()
    role_id: UserRoles;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    bio?: string;
}
