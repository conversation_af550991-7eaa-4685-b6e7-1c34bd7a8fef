import { Field, InputType, Int } from '@nestjs/graphql';
import { RegisterInputDto } from '../../auth/dtos/register-input.dto';
import { IsEnum, IsNotEmpty, IsObject, IsOptional, IsString } from 'class-validator';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { UserStatus } from '../../../entities/user.entity';
import GraphQLJSON from 'graphql-type-json';

@InputType()
export class UserCreateInputDto extends RegisterInputDto {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(UserStatus)
    status_id?: UserStatus;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists('files')
    avatar_id?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    bio?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    @IsObject()
    info?: any;
}
