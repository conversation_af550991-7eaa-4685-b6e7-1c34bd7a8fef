import { Field, InputType } from '@nestjs/graphql';
import { ArrayNotEmpty, IsArray, IsNotEmpty } from 'class-validator';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';

@InputType()
export class ChangeGroupsInputDto {
    @Field(() => [Number])
    @IsArray()
    //@ArrayNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsNotEmpty({ each: true })
    @IdExists('groups', { each: true })
    group_ids: number[];
}
