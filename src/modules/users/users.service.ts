import { Injectable, NotFoundException } from '@nestjs/common';
import { BaseService } from '../../commons/bases/base.service';
import { UserEntity, UserRoles, UserStatus } from '../../entities/user.entity';
import { DeleteResult, In, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { UserCreateInputDto } from './dtos/user-create-input.dto';
import { ResetPasswordInputDto } from './dtos/reset-password-input.dto';
import { ChangeGroupsInputDto } from './dtos/change-groups-input.dto';
import { GroupEntity } from '../../entities/group.entity';
import { isEmpty } from 'lodash';
import { WeaveformAxios } from '../../axios/weaveform.axios';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { UserUpdateInputDto } from './dtos/user-update-input.dto';
import { genPassword } from '../../commons/helpers/hash-code.helper';

@Injectable()
export class UsersService extends BaseService<UserEntity> {
    constructor(
        private readonly weaveformAxios: WeaveformAxios,
        @InjectRepository(UserEntity)
        public readonly repo: Repository<UserEntity>
    ) {
        super(repo);
    }

    async store(body: UserCreateInputDto, auth: UserEntity): Promise<UserEntity> {
        const hashedPassword = await genPassword(body.password);
        return this.repo.manager.transaction(async (run) => {
            const newUser = this.repo.create({
                ...body,
                password: hashedPassword,
                created_by: auth.id,
                status_id: body.status_id || UserStatus.ACTIVE,
            });
            const user = await run.save(newUser);
            if (body.role_id !== UserRoles.ADMIN) {
                await this.weaveformAxios.createUser({
                    email: user.email,
                    first_name: user.first_name,
                    last_name: user?.last_name || '',
                    phone: user.phone_number,
                    password: body.password,
                    role_id: user.role_id,
                });
            }
            return user;
        });
    }

    async update(id: number, body: UserUpdateInputDto, auth: UserEntity): Promise<UserEntity> {
        let user = await this.findOne(id);
        return this.repo.manager.transaction(async (run) => {
            if (!user) {
                throw new NotFoundException();
            }
            const cleanedData = Object.fromEntries(
                Object.entries(body).filter(([_, value]) => value !== undefined)
            ) as QueryDeepPartialEntity<UserEntity>;
            Object.assign(user, cleanedData);
            user.updated_by = auth.id;
            user = await run.save(user);
            if (user.role_id !== UserRoles.ADMIN) {
                await this.weaveformAxios.updateUser({
                    email: user.email,
                    first_name: user.first_name,
                    last_name: user?.last_name || '',
                    phone: user.phone_number,
                    role_id: user.role_id,
                });
            }

            return user;
        });
    }

    async resetPassword(id: number, body: ResetPasswordInputDto, auth: UserEntity): Promise<boolean> {
        const hashedPassword = await genPassword(body.password);
        let user = await this.findOne(id);
        return this.repo.manager.transaction(async (run) => {
            if (!user) {
                throw new NotFoundException();
            }
            user.password = hashedPassword;
            user.updated_by = auth.id;
            user = await run.save(user);
            if (user.role_id !== UserRoles.ADMIN) {
                await this.weaveformAxios.updateUser({
                    email: user.email,
                    password: body.password,
                    role_id: user.role_id,
                });
            }
            return true;
        });
    }

    async changeGroups(id: number, body: ChangeGroupsInputDto): Promise<UserEntity | DeleteResult> {
        const user = await this.findOne(id);
        if (!user) {
            throw new NotFoundException();
        }
        const groups = isEmpty(body.group_ids)
            ? []
            : await this.repo.manager.find(GroupEntity, { where: { id: In(body.group_ids) } });
        return this.repo.manager.transaction(async (run) => {
            if (isEmpty(groups)) {
                return run.delete('user_groups', { user_id: id });
            } else {
                user.groups = groups;
                return run.save(user);
            }
        });
    }
}
