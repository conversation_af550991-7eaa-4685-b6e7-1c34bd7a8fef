import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { UsersService } from './users.service';
import { AuthResolver } from '../../commons/decorators/graphql.decorators';
import { UsersModel } from './models/users.model';
import { UserEntity, UserRoles, UserStatus } from '../../entities/user.entity';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { UserCreateInputDto } from './dtos/user-create-input.dto';
import { UserUpdateInputDto } from './dtos/user-update-input.dto';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { AuthUser } from '../auth/auth.decorator';
import { IPaginatedType } from '../../commons/bases/base.model';
import { ResetPasswordInputDto } from './dtos/reset-password-input.dto';
import { Roles } from '../../commons/decorators/roles.decorator';
import { ChangeGroupsInputDto } from './dtos/change-groups-input.dto';
import { FileEntity } from '../../entities/file.entity';
import { GroupEntity } from '../../entities/group.entity';
import { IndustryEntity } from '../../entities/industry.entity';
import { DataLoaderService } from '../../data-loaders/data-loaders.service';
import { TemplateEntity } from '../../entities/template.entity';
import { TemplateMetaEntity } from '../../entities/template-meta.entity';
import { TransactionEntity } from '../../entities/transaction.entity';

@AuthResolver(UserEntity)
@Roles(UserRoles.ADMIN)
export class UsersResolver {
    constructor(
        private readonly usersService: UsersService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => FileEntity, { nullable: true })
    async avatar(@Parent() user: UserEntity): Promise<FileEntity | null> {
        if (!user.avatar_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(user.avatar_id);
    }

    @ResolveField(() => [GroupEntity], { nullable: true })
    async groups(@Parent() user: UserEntity): Promise<GroupEntity[]> {
        return this.dataLoader.relationBatchManyMany(GroupEntity, 'users').load(user.id);
    }

    @ResolveField(() => IndustryEntity, { nullable: true })
    async industry(@Parent() user: UserEntity): Promise<IndustryEntity | null> {
        if (!user.industry_id) return null;
        return this.dataLoader.relationBatchOne(IndustryEntity).load(user.industry_id);
    }

    @ResolveField(() => [TemplateEntity], { nullable: true })
    async templates(@Parent() user: UserEntity): Promise<TemplateEntity[]> {
        return this.dataLoader.relationBatchOneMany(TemplateEntity, 'designer').load(user.id);
    }

    @ResolveField(() => [TemplateEntity], { nullable: true })
    async userTemplates(@Parent() user: UserEntity): Promise<TemplateEntity[]> {
        return this.dataLoader.relationBatchManyMany(TemplateEntity, 'industries').load(user.id);
    }

    @ResolveField(() => [TransactionEntity], { nullable: true })
    async transactions(@Parent() user: UserEntity): Promise<TransactionEntity[] | null> {
        return this.dataLoader.relationBatchOneMany(TransactionEntity, 'customer').load(user.id);
    }

    @ResolveField(() => [TemplateMetaEntity], { nullable: true })
    async templateMeta(@Parent() user: UserEntity): Promise<TemplateMetaEntity[]> {
        return this.dataLoader.relationBatchOneMany(TemplateMetaEntity, 'user_id').load(user.id);
    }

    @Query(() => UsersModel, { name: 'users_list' })
    async users(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<UserEntity>> {
        return this.usersService.search(body);
    }

    @Query(() => UserEntity, { name: 'users_detail' })
    async view(@Args('id', { type: () => Int }) id: number) {
        return this.usersService.findOne(id).then((res) => {
            if (!res) {
                throw new NotFoundException();
            }
            return res;
        });
    }

    @Mutation(() => UserEntity, { name: 'users_create' })
    async store(@Args('body') body: UserCreateInputDto, @AuthUser() auth: UserEntity): Promise<UserEntity> {
        return this.usersService.store(body, auth);
    }

    @Mutation(() => UserEntity, { name: 'users_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: UserUpdateInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<UserEntity> {
        body['contextId'] = id;
        return this.usersService.update(id, body, auth);
    }

    @Mutation(() => Boolean, { name: 'users_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<Boolean> {
        // Check if user exists and get their status
        const user = await this.usersService.findOne(id);
        if (!user) {
            throw new NotFoundException('User not found');
        }

        // Check if user has active status
        if (user.status_id === UserStatus.ACTIVE) {
            throw new BadRequestException('Cannot delete user with active status');
        }

        await this.usersService.softDelete(id, auth.id);
        return true;
    }

    @Mutation(() => Boolean, { name: 'users_reset_pass' })
    async resetPass(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ResetPasswordInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<Boolean> {
        await this.usersService.resetPassword(id, body, auth);
        return true;
    }

    @Mutation(() => Boolean, { name: 'users_change_groups' })
    async changeGroups(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ChangeGroupsInputDto
    ): Promise<Boolean> {
        await this.usersService.changeGroups(id, body);
        return true;
    }
}
