import { Module } from '@nestjs/common';
import { WebsiteStylesService } from './services/website-styles.service';
import { WebsiteStylesResolver } from './resolvers/website-styles.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WebsiteStyleEntity } from '../../entities/website-style.entity';

@Module({
    imports: [TypeOrmModule.forFeature([WebsiteStyleEntity])],
    providers: [WebsiteStylesService, WebsiteStylesResolver],
})
export class WebsiteStylesModule {}
