import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { WebsiteStyleEntity } from '../../../entities/website-style.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class WebsiteStylesService extends BaseService<WebsiteStyleEntity> {
    constructor(@InjectRepository(WebsiteStyleEntity) private readonly repo: Repository<WebsiteStyleEntity>) {
        super(repo);
    }
}
