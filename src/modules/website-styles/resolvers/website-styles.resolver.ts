import { Args, Int, Mutation, Query } from '@nestjs/graphql';
import { UserEntity, UserRoles } from '../../../entities/user.entity';
import { BaseInput } from '../../../commons/bases/base.input';
import { AuthUser } from '../../auth/auth.decorator';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { Roles } from '../../../commons/decorators/roles.decorator';
import { WebsiteStyleEntity } from '../../../entities/website-style.entity';
import { WebsiteStylesService } from '../services/website-styles.service';
import { WebsiteStyleSaveInputDto } from '../dtos/website-style-save-input.dto';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { ItemStatus } from '../../../commons/enums.common';

@AuthResolver(WebsiteStyleEntity)
@Roles(UserRoles.ADMIN)
export class WebsiteStylesResolver {
    constructor(private readonly websiteStylesService: WebsiteStylesService) {}

    @Query(() => [WebsiteStyleEntity], { name: 'website_style_list' })
    async index(@Args('body') body: BaseInput): Promise<WebsiteStyleEntity[]> {
        return this.websiteStylesService.findAllBy(body);
    }

    @Query(() => WebsiteStyleEntity, { name: 'website_style_view' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<WebsiteStyleEntity> {
        return this.websiteStylesService.findOne(id).then((ws) => {
            if (!ws) throw new NotFoundException();
            return ws;
        });
    }

    @Mutation(() => WebsiteStyleEntity, { name: 'website_style_create' })
    async store(
        @Args('body') body: WebsiteStyleSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<WebsiteStyleEntity> {
        return this.websiteStylesService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => WebsiteStyleEntity, { name: 'website_style_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: WebsiteStyleSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<WebsiteStyleEntity> {
        return this.websiteStylesService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'website_style_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<Boolean> {
        // Check if website style exists and get its status
        const websiteStyle = await this.websiteStylesService.findOne(id);
        if (!websiteStyle) {
            throw new NotFoundException('Website style not found');
        }

        // Check if website style has active status
        if (websiteStyle.status_id === ItemStatus.ACTIVE) {
            throw new BadRequestException('Cannot delete website style with active status');
        }

        await this.websiteStylesService.softDelete(id, auth.id);
        return true;
    }
}
