import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsInt, IsNotEmpty, IsOptional, IsPositive, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { ItemStatus } from '../../../commons/enums.common';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';

@InputType()
export class WebsiteStyleSaveInputDto extends BaseUpdateInputDto {
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @Field()
    name: string;

    @IsOptional()
    @IsString()
    @Field({ nullable: true })
    desc?: string;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(ItemStatus)
    @Field(() => Int)
    status_id: ItemStatus;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @IsPositive()
    @Field(() => Int)
    display_order: number;
}
