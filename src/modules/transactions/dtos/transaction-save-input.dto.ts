import { Field, Float, InputType, Int } from '@nestjs/graphql';
import { IsDate, IsEnum, IsNotEmpty, IsNumber, IsOptional } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { UserEntity } from '../../../entities/user.entity';
import { SubscriptionPlanEntity } from '../../../entities/subscription-plan.entity';
import { WebsiteEntity } from '../../../entities/website.entity';
import { GraphQLJSON } from 'graphql-type-json';
import { SubscriptionStatus, TransactionType } from '../../../entities/transaction.entity';
import { Type } from 'class-transformer';

@InputType()
export class TransactionSaveInputDto {
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(UserEntity)
    @Field(() => Int)
    customer_id: number;

    @IsOptional()
    @IdExists(SubscriptionPlanEntity)
    @Field(() => Int, { nullable: true })
    plan_id?: number;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(TransactionType)
    @Field(() => Int)
    type_id: TransactionType;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsNumber()
    @Field(() => Float)
    price: number;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(SubscriptionStatus)
    @Field(() => Int)
    status_id: SubscriptionStatus;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsDate()
    @Type(() => Date)
    @Field(() => String)
    start_date: Date;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsDate()
    @Type(() => Date)
    @Field(() => String)
    end_date: Date;

    @IsOptional()
    @IdExists(WebsiteEntity)
    @Field(() => Int, { nullable: true })
    website_id?: number;

    @IsOptional()
    @Field(() => GraphQLJSON, { nullable: true })
    info?: any;
}

export interface ITransactionSaveInput {
    customer_id: number;
    plan_id?: number;
    type_id: TransactionType;
    price: number;
    status_id: SubscriptionStatus;
    start_date: Date;
    end_date: Date;
    website_id?: number;
    info?: any;
    created_by?: number;
    updated_by?: number;
}
