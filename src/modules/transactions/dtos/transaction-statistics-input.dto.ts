import { Field, InputType, Int } from '@nestjs/graphql';
import { IsDateString, IsEnum, IsNotEmpty, IsOptional } from 'class-validator';

export enum StatisticsGroupType {
    DAY = 1,
    MONTH = 2,
}

@InputType()
export class TransactionStatisticsInputDto {
    @Field()
    @IsNotEmpty()
    @IsDateString()
    start_date: string; // yyyy-mm-dd format

    @Field({ nullable: true })
    @IsOptional()
    @IsDateString()
    end_date?: string; // yyyy-mm-dd format

    @Field(() => Int)
    @IsEnum(StatisticsGroupType)
    type_id: StatisticsGroupType;
}
