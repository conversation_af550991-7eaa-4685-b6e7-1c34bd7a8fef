import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TransactionEntity } from '../../entities/transaction.entity';
import { BaseService } from '../../commons/bases/base.service';
import { TransactionStatisticsInputDto, StatisticsGroupType } from './dtos/transaction-statistics-input.dto';
import { TransactionStatisticsModel, TransactionTodayStatisticsModel } from './models/transaction-statistics.model';
import { PlanType } from '../../entities/subscription-plan.entity';

@Injectable()
export class TransactionsService extends BaseService<TransactionEntity> {
    constructor(
        @InjectRepository(TransactionEntity)
        public readonly repo: Repository<TransactionEntity>
    ) {
        super(repo);
    }

    async getStatistics(input: TransactionStatisticsInputDto): Promise<TransactionStatisticsModel[]> {
        // Dates are already in yyyy-mm-dd format
        const startDate = input.start_date;
        const endDate = input.end_date || null;

        const dateFormat = input.type_id === StatisticsGroupType.DAY ? 'YYYY-MM-DD' : 'YYYY-MM';
        const dateColumn =
            input.type_id === StatisticsGroupType.DAY ? 'DATE(t.created_at)' : "DATE_TRUNC('month', t.created_at)";

        // Build WHERE clause conditionally
        let whereClause = 'WHERE DATE(t.created_at) >= $1';
        const queryParams = [startDate];

        if (endDate) {
            whereClause += ' AND DATE(t.created_at) <= $2';
            queryParams.push(endDate);
        }

        const trialParamIndex = queryParams.length + 1;
        queryParams.push(PlanType.TRIAL.toString());

        const query = `
            SELECT
                TO_CHAR(${dateColumn}, '${dateFormat}') as label,
                COUNT(CASE WHEN t.end_date >= NOW() AND (p.type_id IS NULL OR p.type_id != $${trialParamIndex}) THEN 1 END) as paid,
                COUNT(CASE WHEN t.end_date >= NOW() AND p.type_id = $${trialParamIndex} THEN 1 END) as trial
            FROM transactions t
            LEFT JOIN subscription_plans p ON t.plan_id = p.id
            ${whereClause}
            GROUP BY ${dateColumn}
            ORDER BY ${dateColumn}
        `;

        const result = await this.repo.query(query, queryParams);

        return result.map((row) => ({
            label: row.label,
            paid: parseInt(row.paid),
            trial: parseInt(row.trial),
        }));
    }

    async getTodayStatistics(): Promise<TransactionTodayStatisticsModel> {
        // Get today's date in YYYY-MM-DD format
        const today = new Date().toISOString().split('T')[0];

        const query = `
            SELECT
                COUNT(*) as total_transactions,
                COUNT(CASE WHEN p.type_id = $1 THEN 1 END) as trial_transactions
            FROM transactions t
            LEFT JOIN subscription_plans p ON t.plan_id = p.id
            WHERE t.end_date >= $2
        `;

        const result = await this.repo.query(query, [PlanType.TRIAL.toString(), today]);

        return {
            total_transactions: parseInt(result[0].total_transactions),
            trial_transactions: parseInt(result[0].trial_transactions),
        };
    }
}
