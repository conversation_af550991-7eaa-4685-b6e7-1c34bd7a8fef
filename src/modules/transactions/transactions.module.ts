import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TransactionEntity } from '../../entities/transaction.entity';
import { TransactionsService } from './transactions.service';
import { TransactionsResolver } from './transactions.resolver';

@Module({
    imports: [TypeOrmModule.forFeature([TransactionEntity])],
    providers: [TransactionsService, TransactionsResolver],
})
export class TransactionsModule {}
