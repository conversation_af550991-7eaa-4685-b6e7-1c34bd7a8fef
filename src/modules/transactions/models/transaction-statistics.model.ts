import { Field, Int, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class TransactionStatisticsModel {
    @Field()
    label: string;

    @Field(() => Int)
    paid: number;

    @Field(() => Int)
    trial: number;
}

@ObjectType()
export class TransactionTodayStatisticsModel {
    @Field(() => Int)
    total_transactions: number;

    @Field(() => Int)
    trial_transactions: number;
}
