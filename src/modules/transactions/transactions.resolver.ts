import { Args, Int, Parent, Query, ResolveField } from '@nestjs/graphql';
import { TransactionsService } from './transactions.service';
import { TransactionEntity } from '../../entities/transaction.entity';
import { TransactionSaveInputDto } from './dtos/transaction-save-input.dto';
import { UserEntity, UserRoles } from '../../entities/user.entity';
import { AuthMutation, AuthResolver } from '../../commons/decorators/graphql.decorators';
import { AuthUser } from '../auth/auth.decorator';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { IPaginatedType } from '../../commons/bases/base.model';
import { TransactionModel } from './models/transaction.model';
import { TransactionStatisticsModel, TransactionTodayStatisticsModel } from './models/transaction-statistics.model';
import { TransactionStatisticsInputDto } from './dtos/transaction-statistics-input.dto';
import { Roles } from '../../commons/decorators/roles.decorator';
import { SubscriptionPlanEntity } from '../../entities/subscription-plan.entity';
import { WebsiteEntity } from '../../entities/website.entity';
import { DataLoaderService } from '../../data-loaders/data-loaders.service';

@AuthResolver(TransactionEntity)
@Roles(UserRoles.ADMIN)
export class TransactionsResolver {
    constructor(
        private readonly transactionsService: TransactionsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => SubscriptionPlanEntity, { nullable: true })
    async plan(@Parent() transaction: TransactionEntity): Promise<SubscriptionPlanEntity | null> {
        if (!transaction.plan_id) return null;
        return this.dataLoader.relationBatchOne(SubscriptionPlanEntity).load(transaction.plan_id);
    }

    @ResolveField(() => WebsiteEntity, { nullable: true })
    async website(@Parent() transaction: TransactionEntity): Promise<WebsiteEntity | null> {
        if (!transaction.website_id) return null;
        return this.dataLoader.relationBatchOne(WebsiteEntity).load(transaction.website_id);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async customer(@Parent() transaction: TransactionEntity): Promise<UserEntity | null> {
        if (!transaction.customer_id) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(transaction.customer_id);
    }

    @Query(() => TransactionModel, { name: 'transactions_list' })
    async index(@Args('input') input: BasePaginationInput): Promise<IPaginatedType<TransactionEntity>> {
        return this.transactionsService.search(input);
    }

    @Query(() => [TransactionStatisticsModel], { name: 'transactions_statistics' })
    async statistics(@Args('input') input: TransactionStatisticsInputDto): Promise<TransactionStatisticsModel[]> {
        return this.transactionsService.getStatistics(input);
    }

    @Query(() => TransactionTodayStatisticsModel, { name: 'transactions_today_statistics' })
    async todayStatistics(): Promise<TransactionTodayStatisticsModel> {
        return this.transactionsService.getTodayStatistics();
    }

    @AuthMutation(() => TransactionEntity, { name: 'transactions_create' })
    async create(
        @Args('input') input: TransactionSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<TransactionEntity> {
        return this.transactionsService.create({
            ...input,
            created_by: auth.id,
        });
    }

    @AuthMutation(() => TransactionEntity, { name: 'transactions_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('input') input: TransactionSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<TransactionEntity> {
        return this.transactionsService.updateOne(id, {
            ...input,
            updated_by: auth.id,
        });
    }

    // @AuthMutation(() => Boolean, { name: 'transactions_delete' })
    // async delete(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
    //     await this.transactionsService.softDelete(id, auth.id);
    //     return true;
    // }
}
