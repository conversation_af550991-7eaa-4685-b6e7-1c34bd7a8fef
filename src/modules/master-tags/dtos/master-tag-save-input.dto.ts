import { Field, InputType } from '@nestjs/graphql';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { IsUnique } from '../../../commons/validators/is-unique.validator';
import { MasterTagEntity } from '../../../entities/master-tag.entity';

@InputType()
export class MasterTagSaveInputDto extends BaseUpdateInputDto {
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @IsUnique(MasterTagEntity)
    @Field()
    name: string;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @Field()
    type: string;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @Field()
    display_name: string;

    @IsOptional()
    @IsString()
    @Field({ nullable: true })
    tool_tip?: string;
}

export interface IMasterTagSaveInput {
    name: string;
    type: string;
    display_name: string;
    tool_tip?: string;
    created_by?: number;
    updated_by?: number;
}
