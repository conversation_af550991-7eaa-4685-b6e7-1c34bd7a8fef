import { Module } from '@nestjs/common';
import { MasterTagsService } from './master-tags.service';
import { MasterTagsResolver } from './master-tags.resolver';
import { MasterTagsController } from './master-tags.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MasterTagEntity } from '../../entities/master-tag.entity';

@Module({
    imports: [TypeOrmModule.forFeature([MasterTagEntity])],
    controllers: [MasterTagsController],
    providers: [MasterTagsService, MasterTagsResolver],
    exports: [MasterTagsService],
})
export class MasterTagsModule {}
