import { Injectable } from '@nestjs/common';
import { BaseService } from '../../commons/bases/base.service';
import { MasterTagEntity } from '../../entities/master-tag.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { CsvRow } from './helpers/master-tag-csv.helper';

@Injectable()
export class MasterTagsService extends BaseService<MasterTagEntity> {
    constructor(
        @InjectRepository(MasterTagEntity)
        public readonly repo: Repository<MasterTagEntity>
    ) {
        super(repo);
    }

    /**
     * X<PERSON> lý import CSV data
     * Nếu name đã tồn tại thì update, nếu chưa có thì insert
     */
    async importFromCsv(
        csvData: CsvRow[],
        userId: number
    ): Promise<{ created: number; updated: number; errors: string[] }> {
        let created = 0;
        let updated = 0;
        const errors: string[] = [];

        for (const row of csvData) {
            try {
                // <PERSON><PERSON><PERSON> tra xem name đã tồn tại chưa
                const existingTag = await this.repo.findOne({
                    where: { name: row.name },
                });

                if (existingTag) {
                    // Update existing record
                    await this.repo.update(existingTag.id, {
                        type: row.type,
                        display_name: row.display_name,
                        tool_tip: row.tool_tip,
                        updated_by: userId,
                        updated_at: new Date(),
                    });
                    updated++;
                } else {
                    // Create new record
                    await this.repo.save({
                        name: row.name,
                        type: row.type,
                        display_name: row.display_name,
                        tool_tip: row.tool_tip,
                        created_by: userId,
                    });
                    created++;
                }
            } catch (error) {
                errors.push(`Lỗi xử lý dòng "${row.name}": ${error.message}`);
            }
        }

        return { created, updated, errors };
    }
}
