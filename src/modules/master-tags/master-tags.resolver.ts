import { Args, Int, Query, Resolver } from '@nestjs/graphql';
import { MasterTagEntity } from '../../entities/master-tag.entity';
import { MasterTagsService } from './master-tags.service';
import { MasterTagSaveInputDto } from './dtos/master-tag-save-input.dto';
import { AuthMutation } from '../../commons/decorators/graphql.decorators';
import { Roles } from '../../commons/decorators/roles.decorator';
import { UserEntity, UserRoles } from '../../entities/user.entity';
import { AuthUser } from '../auth/auth.decorator';
import { NotFoundException } from '@nestjs/common';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { IPaginatedType } from '../../commons/bases/base.model';
import { MasterTagModel } from './models/master-tag.model';

@Resolver(MasterTagEntity)
export class MasterTagsResolver {
    constructor(private readonly masterTagsService: MasterTagsService) {}

    @Query(() => MasterTagModel, { name: 'master_tags_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<MasterTagEntity>> {
        return this.masterTagsService.search(body);
    }

    @Query(() => MasterTagEntity, { name: 'master_tags_view' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<MasterTagEntity> {
        return this.masterTagsService.findOne(id).then((item) => {
            if (!item) throw new NotFoundException();
            return item;
        });
    }

    @AuthMutation(() => MasterTagEntity, { name: 'master_tags_create' })
    @Roles([UserRoles.ADMIN])
    async store(@Args('body') body: MasterTagSaveInputDto, @AuthUser() auth: UserEntity): Promise<MasterTagEntity> {
        return this.masterTagsService.create({ ...body, created_by: auth.id });
    }

    @AuthMutation(() => MasterTagEntity, { name: 'master_tags_update' })
    @Roles([UserRoles.ADMIN])
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: MasterTagSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<MasterTagEntity> {
        return this.masterTagsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @AuthMutation(() => Boolean, { name: 'master_tags_delete' })
    @Roles([UserRoles.ADMIN])
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.masterTagsService.softDelete(id, auth.id);
        return true;
    }
}
