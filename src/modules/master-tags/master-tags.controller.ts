import {
    BadRequestException,
    Controller,
    InternalServerErrorException,
    Post,
    UploadedFile,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { AuthUser } from '../auth/auth.decorator';
import { UserEntity, UserRoles } from '../../entities/user.entity';
import { MasterTagsService } from './master-tags.service';
import { masterTagsMulterOptions } from './configs/multer.config';
import { CsvRow, MasterTagCsvHelper } from './helpers/master-tag-csv.helper';
import { Roles } from '../../commons/decorators/roles.decorator';
import appConf from '../../configs/app.conf';
import { convertUploadPath } from '../../commons/helpers/file.helper';

@Controller('master-tags')
export class MasterTagsController {
    constructor(private readonly masterTagsService: MasterTagsService) {}

    @Post('/import')
    @UseGuards(JwtAuthGuard)
    @Roles([UserRoles.ADMIN])
    @UseInterceptors(FileInterceptor('file', masterTagsMulterOptions))
    async importCsv(@UploadedFile() file: Express.Multer.File, @AuthUser() auth: UserEntity) {
        if (!file) {
            throw new BadRequestException('Vui lòng chọn file CSV để upload');
        }

        let csvData: CsvRow[];
        let errors: string[];
        let result: { created: any; updated: any; errors: any };

        // 1. Parse CSV file
        try {
            const csvResult = await MasterTagCsvHelper.parseCsvFile(file.path);
            csvData = csvResult.csvData;
            errors = csvResult.errors;
        } catch (error) {
            if (error instanceof BadRequestException) {
                throw error;
            }
            throw new BadRequestException(`Lỗi đọc file CSV: ${error.message}`);
        }

        if (csvData.length === 0) {
            throw new BadRequestException('File CSV không có dữ liệu hợp lệ');
        }

        // 2. Import data vào database
        try {
            result = await this.masterTagsService.importFromCsv(csvData, auth.id);
        } catch (error) {
            throw new InternalServerErrorException(`Lỗi import dữ liệu: ${error.message}`);
        }

        return {
            master_tags_import: {
                totalProcessed: csvData.length,
                created: result.created,
                updated: result.updated,
                errors: [...errors, ...result.errors],
                fileName: file.filename,
                filePath: appConf.API_URL + convertUploadPath(file.path),
            },
        };
    }
}
