import { diskStorage, FileFilterCallback, Options } from 'multer';
import { mkdir } from '../../../commons/helpers/common.helper';
import { BadRequestException } from '@nestjs/common';
import { Request } from 'express';
import { UPLOAD_DIR } from '../../../commons/helpers/file.helper';
import appConf from '../../../configs/app.conf';
import slugify from 'slugify';

export const masterTagsMulterConfig = {
    dest: `./${UPLOAD_DIR}/master-tags`, // Thư mục lưu file riêng cho master-tags
    limitSize: appConf.LIMIT_UPLOAD_SIZE * 1024 * 1024, // Giới hạn dung lượng file upload theo MB
};

export const masterTagsMulterOptions: Options = {
    storage: diskStorage({
        destination: (_, file, cb) => {
            // Tạo thư mục theo ngày
            const subPath = `/${new Date().toISOString().slice(0, 10)}`;
            const uploadPath = masterTagsMulterConfig.dest + subPath;
            mkdir(uploadPath);
            cb(null, uploadPath);
        },
        filename: (_, file, cb) => {
            const randomName = Date.now() + '-' + Math.round(Math.random() * 1e9);
            cb(null, `${randomName}_${slugify(file.originalname)}`);
        },
    }),
    fileFilter: (_: Request, file: Express.Multer.File, cb: FileFilterCallback) => {
        // Chỉ cho phép file CSV
        const isCSV = file.mimetype === 'text/csv' && file.originalname.toLowerCase().endsWith('.csv');

        if (isCSV) {
            cb(null, true);
        } else {
            cb(new BadRequestException('Chỉ chấp nhận file CSV!') as any, false);
        }
    },
    limits: {
        fileSize: masterTagsMulterConfig.limitSize,
    },
};
