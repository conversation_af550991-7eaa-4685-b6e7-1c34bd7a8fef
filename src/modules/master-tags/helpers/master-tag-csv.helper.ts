import * as fs from 'fs';
import * as csvParser from 'csv-parser';
import { BadRequestException } from '@nestjs/common';

export interface CsvRow {
    name: string;
    type: string;
    display_name: string;
    tool_tip?: string;
}

export interface CsvResult {
    csvData: CsvRow[];
    errors: string[];
}

export class MasterTagCsvHelper {
    static async parseCsvFile(filePath: string): Promise<CsvResult> {
        if (!fs.existsSync(filePath)) {
            throw new BadRequestException('File không tồn tại');
        }

        const csvData: CsvRow[] = [];
        const errors: string[] = [];
        let rowIndex = 1; // bắt đầu từ 1 để tính dòng thứ 2 (do bỏ header)
        //type hợp lệ image, button, text, link, iframe, video, icon
        const allowTypes: string[] = ['image', 'button', 'text', 'link', 'iframe', 'video', 'icon'];

        return new Promise((resolve, reject) => {
            fs.createReadStream(filePath)
                .pipe(csvParser())
                .on('data', (row) => {
                    rowIndex++;
                    try {
                        const values = Object.values(row);
                        const name = values[1]?.toString().trim();
                        const type = values[2]?.toString().trim().toLowerCase();
                        const display_name = values[3]?.toString().trim();
                        const tool_tip = values[4]?.toString().trim();
                        if (!name || !type || !display_name || !tool_tip) {
                            errors.push(`Dòng ${rowIndex} thiếu dữ liệu bắt buộc`);
                            return;
                        }
                        if (!allowTypes.includes(type)) {
                            errors.push(`Dòng ${rowIndex} có type không hợp lệ`);
                            return;
                        }
                        csvData.push({ name, type, display_name, tool_tip });
                    } catch (err) {
                        errors.push(`Lỗi xử lý dòng ${rowIndex}: ${err.message}`);
                    }
                })
                .on('end', () => {
                    if (csvData.length === 0) {
                        reject(new BadRequestException('Không tìm thấy dữ liệu hợp lệ trong file CSV'));
                    } else {
                        resolve({ csvData, errors });
                    }
                })
                .on('error', (err) => {
                    reject(new BadRequestException(`Không thể đọc file CSV: ${err.message}`));
                });
        });
    }
}
