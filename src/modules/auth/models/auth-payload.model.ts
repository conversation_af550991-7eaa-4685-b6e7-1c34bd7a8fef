import { Field, ObjectType, ID, GraphQLISODateTime } from '@nestjs/graphql';
import { GraphQLJSON } from 'graphql-type-json';
import { UserRoles } from '../../../entities/user.entity';

@ObjectType()
export class TokenPayloadModel {
    @Field(() => String)
    access_token: string;

    @Field(() => String)
    refresh_token: string;
}

@ObjectType()
export class AuthPayloadModel {
    @Field(() => ID)
    id: number;

    @Field()
    first_name: string;

    @Field(() => String, { nullable: true })
    last_name?: string | null;

    @Field(() => String, { nullable: true })
    avatar?: string | null;

    @Field(() => UserRoles)
    role_id: UserRoles;

    @Field(() => String, { nullable: true })
    uid?: string | null;

    @Field(() => GraphQLJSON, { nullable: true })
    info?: any;

    @Field(() => GraphQLISODateTime)
    created_at: Date;

    @Field(() => TokenPayloadModel)
    token: TokenPayloadModel;
}
