import { BadRequestException, Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { MoreThan, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { BaseService } from '../../../commons/bases/base.service';
import { UserEntity, UserRoles, UserStatus } from '../../../entities/user.entity';
import { RegisterInputDto } from '../dtos/register-input.dto';
import { LoginInputDto } from '../dtos/login-input.dto';
import {
    comparePassword,
    compareToken,
    encryptToken,
    genPassword,
    sha256Encode,
} from '../../../commons/helpers/hash-code.helper';
import { PersonalTokenService } from './personal-token.service';
import { AuthPayloadModel } from '../models/auth-payload.model';
import { ProfileInputDto } from '../dtos/profile-input.dto';
import { ChangePassInputDto } from '../dtos/change-pass-input.dto';
import { ForgotPasswordDto } from '../dtos/forgot-password.dto';
import { PasswordResetEntity } from '../../../entities/password-reset.entity';
import { TooManyRequestsException } from '../../../commons/exceptions/too-many-requests.exception';
import { randomNumber, randomString } from '../../../commons/helpers/string.helper';
import appConf from '../../../configs/app.conf';
import { addHours } from 'date-fns';
import { ResetPasswordDto } from '../dtos/reset-password.dto';
import { EmailService } from '../../e-mails/email.service';

export interface IJwtPayload {
    id: number;
    first_name: string;
    last_name?: string | null;
    avatar?: string | null;
    role_id: UserRoles;
    uid?: string | null;
    info?: any;
    created_at: Date;
}

@Injectable()
export class AuthService extends BaseService<UserEntity> {
    constructor(
        private readonly jwtService: JwtService,
        private readonly personalTokenService: PersonalTokenService,
        private readonly emailService: EmailService,
        @InjectRepository(UserEntity)
        public readonly repo: Repository<UserEntity>
    ) {
        super(repo);
    }

    private async validateUser(body: LoginInputDto): Promise<any> {
        const user = await this.repo.findOne({
            where: { email: body.email, role_id: body.role_id },
            relations: ['avatar'],
        });

        if (!user || user.status_id !== UserStatus.ACTIVE) {
            throw new BadRequestException('Invalid email or password');
        }

        // Kiểm tra mật khẩu (compare với bcrypt hash)
        const isMatch = await comparePassword(user.password ?? '', body.password);
        if (!isMatch) {
            throw new BadRequestException('Invalid email or password');
        }

        // Xóa mật khẩu trước khi trả về
        const { password: _, ...result } = user;
        return result;
    }

    private async genJWTWithUser(user: UserEntity): Promise<AuthPayloadModel> {
        const userLogin: IJwtPayload = {
            id: user.id,
            first_name: user.first_name,
            last_name: user.last_name,
            avatar: user.avatar?.file_url ?? null,
            role_id: user.role_id,
            uid: user.uid,
            info: user.info,
            created_at: new Date(user.created_at),
        };
        const rtTimeOut = appConf.RT_TIMEOUT;
        const [access_token, refresh_token] = await Promise.all([
            this.jwtService.signAsync(userLogin, {
                secret: appConf.AT_SECRET,
                expiresIn: appConf.AT_TIMEOUT,
            }),
            this.jwtService.signAsync(userLogin, {
                secret: appConf.RT_SECRET,
                expiresIn: rtTimeOut,
            }),
        ]);
        return { ...userLogin, token: { access_token, refresh_token } };
    }

    private async createTokenUser(user: UserEntity): Promise<AuthPayloadModel> {
        const result = await this.genJWTWithUser(user);
        const [access_token, refresh_token, expires_at] = await Promise.all([
            encryptToken(result.token.access_token),
            encryptToken(result.token.refresh_token),
            this.jwtService.verify(result.token.refresh_token, {
                secret: appConf.RT_SECRET || 'rtSecretKey',
            }).exp,
        ]);
        try {
            let isEnable = true;
            await this.personalTokenService.save({
                user_id: user.id,
                access_token,
                refresh_token,
                expires_at: new Date(expires_at * 1000),
                is_enable: isEnable,
            });
            return result;
        } catch (e) {
            throw new InternalServerErrorException();
        }
    }

    async login(body: LoginInputDto): Promise<AuthPayloadModel> {
        const user = await this.validateUser(body);
        return this.createTokenUser(user);
    }

    async register(body: RegisterInputDto): Promise<UserEntity> {
        const hashedPassword = await genPassword(body.password);

        const newUser = this.repo.create({ ...body, password: hashedPassword, status_id: UserStatus.ACTIVE });
        return this.repo.save(newUser);
    }

    async logout(auth: UserEntity, token: string): Promise<void> {
        const personalTokens = await this.personalTokenService.find({
            where: { user_id: auth.id },
            select: ['id', 'access_token'],
        });
        let tokenId: number = 0;
        for (const x of personalTokens) {
            const flag = await compareToken(token, x.access_token);
            if (flag) {
                tokenId = x.id;
                break;
            }
        }
        if (tokenId) await this.personalTokenService.delete(tokenId);
    }

    async refreshToken(refreshToken: string): Promise<AuthPayloadModel> {
        const refresh = new JwtService({
            secret: appConf.RT_SECRET || 'rtSecretKey',
        });
        const auth = refresh.verify(refreshToken);
        const user = await this.findOne({ where: { id: auth.id }, relations: ['avatar'] });
        if (!user) throw new BadRequestException();
        const personalTokens = await this.personalTokenService.find({
            where: { user_id: user.id, is_enable: true },
            select: ['id', 'refresh_token'],
        });
        let flag = false;
        let tokenId: number = 0;
        for (const x of personalTokens) {
            flag = await compareToken(refreshToken, x.refresh_token);
            if (flag) {
                tokenId = x.id;
                break;
            }
        }
        if (!flag) throw new BadRequestException();
        await this.personalTokenService.delete(tokenId).catch((e) => {
            console.log('⚠️ Error: ', e.message);
        });
        return this.createTokenUser(user);
    }

    async updateProfile(id: number, body: ProfileInputDto): Promise<UserEntity> {
        const user = await this.findOne({ where: { id } });
        if (!user) throw new NotFoundException();
        const newUser = this.repo.create({ ...user, ...body });
        return this.save(newUser);
    }

    async changePassword(body: ChangePassInputDto, auth: UserEntity): Promise<void> {
        const check = await comparePassword(auth.password ?? '', body.old_password);
        if (!check) throw new BadRequestException();
        const password = await genPassword(body.password);
        await this.updateOne(auth.id, { password, updated_by: auth.id });
    }

    async forgotPassword(body: ForgotPasswordDto): Promise<string> {
        const user = await this.findOne({
            where: { email: body.email, role_id: body.role_id },
        });
        if (!user) throw new NotFoundException();
        const pwRepo = this.repo.manager.getRepository(PasswordResetEntity);
        const pw = await pwRepo
            .createQueryBuilder()
            .where('user_id = :userId', { userId: user.id })
            .andWhere('created_at::date = CURRENT_DATE')
            .getOne();
        if (pw && pw.number_sent >= appConf.LIMIT_SEND_FORGOT_PASS) {
            throw new TooManyRequestsException();
        }
        const now = new Date();
        const token = `${now.getTime().toString()}.${randomString(32)}`;
        const otp = randomNumber(6);
        const newPw = {
            id: pw?.id,
            number_sent: (pw?.number_sent ?? 0) + 1,
            user_id: user.id,
            token: sha256Encode(token),
            otp: sha256Encode(otp),
            expires_at: addHours(now, appConf.OTP_EXPIRATION_TIME),
            deleted_at: null,
        } as unknown as PasswordResetEntity;
        return pwRepo
            .save(newPw)
            .catch(() => {
                throw new InternalServerErrorException();
            })
            .then(() => {
                this.emailService.sendForgotPasswordEmail(user.first_name, user.email, otp).catch((e) => {
                    console.log('⚠️ Send Mail Error: ', e.message);
                });
                return token;
            });
    }

    async resetPassword(body: ResetPasswordDto) {
        const pwRepo = this.repo.manager.getRepository(PasswordResetEntity);
        const pw = await pwRepo.findOne({
            where: {
                token: sha256Encode(body.token),
                otp: sha256Encode(body.otp),
                expires_at: MoreThan(new Date()),
            },
        });
        if (!pw) throw new BadRequestException();
        if (!pw.user_id) throw new InternalServerErrorException();
        const password = await genPassword(body.password);
        await this.repo.manager.transaction(async (run) => {
            await run.save(UserEntity, { password, id: pw.user_id });
            await run.softDelete(PasswordResetEntity, { id: pw.id });
        });
    }
}
