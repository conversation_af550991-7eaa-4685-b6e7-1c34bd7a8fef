import { IsNotEmpty, IsString } from 'class-validator';
import { Field, InputType } from '@nestjs/graphql';
import { EqualField } from '../../../commons/validators/equal-field.validator';
import { IsPassword } from '../../../commons/validators/is-password.validator';

@InputType()
export class ResetPasswordDto {
    @Field()
    @IsString()
    @IsNotEmpty()
    token: string;

    @Field()
    @IsString()
    @IsNotEmpty()
    otp: string;

    @Field()
    @IsString()
    @IsNotEmpty()
    @IsPassword()
    password: string;

    @Field()
    @IsNotEmpty()
    @EqualField('password')
    confirm_password: string;
}
