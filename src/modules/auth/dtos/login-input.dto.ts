import { IsEmail, IsEnum, <PERSON>NotEmpty, IsString } from 'class-validator';
import { Field, InputType, Int } from '@nestjs/graphql';
import { UserRoles } from '../../../entities/user.entity';

@InputType()
export class LoginInputDto {
    @Field()
    @IsEmail({})
    @IsString()
    @IsNotEmpty()
    email: string;

    @Field()
    @IsString()
    @IsNotEmpty()
    password: string;

    @Field(() => Int)
    @IsEnum(UserRoles)
    role_id: UserRoles;
}
