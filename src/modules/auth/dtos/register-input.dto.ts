import { IsEmail, <PERSON>Enum, IsISO<PERSON><PERSON>, IsNotEmpty, <PERSON><PERSON>ptional, IsString } from 'class-validator';
import { Field, InputType, Int } from '@nestjs/graphql';
import { Genders, UserRoles } from '../../../entities/user.entity';
import { EqualField } from '../../../commons/validators/equal-field.validator';
import { IsUniqueEmailByRole } from '../../../commons/validators/unique-email.validator';
import { IsPassword } from '../../../commons/validators/is-password.validator';
import { userValidationMessages, validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { IndustryEntity } from '../../../entities/industry.entity';

@InputType()
export class RegisterInputDto {
    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    first_name: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    last_name?: string;

    @Field()
    @IsNotEmpty({ message: userValidationMessages.REQUIRED_EMAIL })
    @IsEmail({}, { message: userValidationMessages.INVALID_EMAIL })
    @IsString()
    @IsUniqueEmailByRole()
    email: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsEnum(Genders)
    gender_id?: Genders;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    phone_number?: string;

    @Field()
    @IsNotEmpty({ message: userValidationMessages.REQUIRED_PASSWORD })
    @IsString()
    @IsPassword()
    password: string;

    @Field()
    @IsNotEmpty({ message: userValidationMessages.REQUIRED_CONFIRM_PASSWORD })
    @EqualField('password', { message: userValidationMessages.CONFIRM_PASSWORD })
    confirm_password: string;

    @Field(() => Int)
    @IsEnum(UserRoles)
    role_id: UserRoles;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @IsISO8601({})
    birthday?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @IsNotEmpty()
    address?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IdExists(IndustryEntity)
    industry_id?: number;
}
