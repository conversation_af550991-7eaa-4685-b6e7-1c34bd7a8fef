import { IsEmail, IsEnum, IsISO8601, IsNotEmpty, IsObject, IsOptional, IsString } from 'class-validator';
import { Field, InputType, Int } from '@nestjs/graphql';
import { Genders } from '../../../entities/user.entity';
import { userValidationMessages, validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { IndustryEntity } from '../../../entities/industry.entity';
import { IsUniqueEmailByRole } from '../../../commons/validators/unique-email.validator';
import Graph<PERSON>JSON from 'graphql-type-json';

@InputType()
export class ProfileInputDto {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists('files')
    avatar_id?: number;

    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    first_name: string;

    @Field({ nullable: true })
    @IsString()
    last_name: string;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEmail({}, { message: userValidationMessages.INVALID_EMAIL })
    @IsString()
    @IsUniqueEmailByRole()
    email: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsEnum(Genders)
    gender_id?: Genders;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    phone_number?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @IsISO8601({})
    birthday?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @IsNotEmpty()
    address?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IdExists(IndustryEntity)
    industry_id?: number;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    @IsObject()
    info?: any;
}
