import { Args, Query, Resolver } from '@nestjs/graphql';
import { GraphQLJSON } from 'graphql-type-json';
import { NamecheapAxios } from '../../axios/namecheap.axios';

@Resolver()
export class NamecheapResolver {
    constructor(private readonly namecheapAxios: NamecheapAxios) {}

    @Query(() => GraphQLJSON, {
        name: 'name_cheap_index',
        description: `example: Command=namecheap.domains.check&DomainList=xyz.com`,
    })
    async index(@Args('query_string') queryString: string) {
        return this.namecheapAxios.exect(queryString);
    }
}
