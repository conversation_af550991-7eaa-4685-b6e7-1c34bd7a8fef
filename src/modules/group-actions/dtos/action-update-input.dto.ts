import { Field, InputType, PartialType } from '@nestjs/graphql';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ActionCreateInputDto } from './action-create-input.dto';

@InputType()
export class ActionUpdateInputDto extends PartialType(ActionCreateInputDto) {
    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsNotEmpty()
    @IsString()
    declare name?: string;
}
