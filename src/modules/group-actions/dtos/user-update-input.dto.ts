import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsOptional } from 'class-validator';
import { UserStatus } from '../../../entities/user.entity';
import { ProfileInputDto } from '../../auth/dtos/profile-input.dto';

@InputType()
export class UserUpdateInputDto extends ProfileInputDto {
    @Field(() => Int)
    @IsOptional()
    @IsEnum(UserStatus)
    status_id: UserStatus;
}
