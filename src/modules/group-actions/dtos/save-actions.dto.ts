import { ArrayNotEmpty, IsArray, IsNotEmpty } from 'class-validator';
import { Field, InputType, Int } from '@nestjs/graphql';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';

@InputType()
export class SaveActionsDto {
    @Field(() => [Int])
    @IsArray()
    @ArrayNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsNotEmpty({ each: true })
    @IdExists('actions', { each: true })
    action_ids: number[];
}

export interface ISaveActions {
    action_ids: number[];
    group_id: number;
    created_by: number;
}
