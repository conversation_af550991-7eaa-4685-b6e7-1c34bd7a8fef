import { <PERSON>rg<PERSON>, <PERSON><PERSON>, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { DataLoaderService } from '../../data-loaders/data-loaders.service';
import { CronTemplateEntity } from '../../entities/cron-template.entity';
import { TemplateEntity } from '../../entities/template.entity';
import { NotFoundException } from '@nestjs/common';
import { CheckCronTemplateInputDto } from './dtos/check-cron-template-input.dto';
import { CronTemplatesService } from './cron-templates.service';
import { AuthResolver } from '../../commons/decorators/graphql.decorators';

@AuthResolver(CronTemplateEntity)
export class CronTemplatesResolver {
    constructor(
        private readonly dataLoaderService: DataLoaderService,
        private readonly cronTemplatesService: CronTemplatesService
    ) {}

    @ResolveField(() => TemplateEntity)
    async template(@Parent() cronTemplate: CronTemplateEntity): Promise<TemplateEntity | null> {
        if (!cronTemplate.template_id) throw new NotFoundException();
        return this.dataLoaderService.relationBatchOne(TemplateEntity).load(cronTemplate.template_id);
    }

    @Query(() => Boolean, { name: 'cron_template_check_exits' })
    async checkExits(@Args('body') body: CheckCronTemplateInputDto): Promise<boolean> {
        return this.cronTemplatesService.checkExits(body);
    }
}
