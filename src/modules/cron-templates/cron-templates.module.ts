import { Module } from '@nestjs/common';
import { CronTemplatesService } from './cron-templates.service';
import { CronTemplatesResolver } from './cron-templates.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CronTemplateEntity } from '../../entities/cron-template.entity';
import { TemplateEntity } from '../../entities/template.entity';

@Module({
    imports: [TypeOrmModule.forFeature([CronTemplateEntity, TemplateEntity])],
    providers: [CronTemplatesService, CronTemplatesResolver],
    exports: [CronTemplatesService],
})
export class CronTemplatesModule {}
