import { Injectable } from '@nestjs/common';
import { BaseService } from '../../commons/bases/base.service';
import { CronTemplateEntity } from '../../entities/cron-template.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CheckCronTemplateInputDto } from './dtos/check-cron-template-input.dto';
import { WebsiteStatus } from '../../entities/website.entity';

@Injectable()
export class CronTemplatesService extends BaseService<CronTemplateEntity> {
    constructor(
        @InjectRepository(CronTemplateEntity)
        public readonly repo: Repository<CronTemplateEntity>
    ) {
        super(repo);
    }

    async checkExits(body: CheckCronTemplateInputDto): Promise<boolean> {
        if (await this.existsBy({ domain: body.domain })) return true;
        await this.create({ ...body, status_id: WebsiteStatus.WAITING });
        return true;
    }
}
