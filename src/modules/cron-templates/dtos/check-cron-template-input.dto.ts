import { Field, InputType, Int } from '@nestjs/graphql';
import { TemplateEntity } from '../../../entities/template.entity';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';

@InputType()
export class CheckCronTemplateInputDto {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsNotEmpty()
    @IdExists(TemplateEntity)
    template_id?: number;

    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    domain: string;
}
