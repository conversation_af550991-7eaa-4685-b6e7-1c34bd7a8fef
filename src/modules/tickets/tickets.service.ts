import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TicketEntity } from '../../entities/ticket.entity';
import { BaseService } from '../../commons/bases/base.service';

@Injectable()
export class TicketsService extends BaseService<TicketEntity> {
    constructor(
        @InjectRepository(TicketEntity)
        public readonly repo: Repository<TicketEntity>
    ) {
        super(repo);
    }
}
