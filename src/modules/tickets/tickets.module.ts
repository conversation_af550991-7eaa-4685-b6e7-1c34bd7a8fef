import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TicketEntity } from '../../entities/ticket.entity';
import { TicketsService } from './tickets.service';
import { TicketsResolver } from './tickets.resolver';

@Module({
    imports: [TypeOrmModule.forFeature([TicketEntity])],
    providers: [TicketsService, TicketsResolver],
    exports: [TicketsService],
})
export class TicketsModule {}
