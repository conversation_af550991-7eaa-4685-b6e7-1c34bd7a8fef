import { Args, Int, Mu<PERSON>, <PERSON>rent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { BadRequestException, NotFoundException, UseGuards } from '@nestjs/common';
import { TicketEntity } from '../../entities/ticket.entity';
import { TicketsService } from './tickets.service';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { TicketModel } from './models/ticket.model';
import { IPaginatedType } from '../../commons/bases/base.model';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { Roles } from '../../commons/decorators/roles.decorator';
import { UserRoles, UserEntity } from '../../entities/user.entity';
import { WebsiteEntity } from '../../entities/website.entity';
import { DataLoaderService } from '../../data-loaders/data-loaders.service';
import { AuthUser } from '../auth/auth.decorator';
import { TicketCreateInputDto } from './dtos/ticket-create-input.dto';
import { TicketUpdateInputDto } from './dtos/ticket-update-input.dto';
import { TicketStatus } from '../../entities/ticket.entity';

@Resolver(() => TicketEntity)
@UseGuards(JwtAuthGuard)
@Roles(UserRoles.ADMIN)
export class TicketsResolver {
    constructor(
        private readonly ticketsService: TicketsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @Query(() => TicketModel, { name: 'tickets_list' })
    async tickets(@Args('input') input: BasePaginationInput): Promise<IPaginatedType<TicketEntity>> {
        return this.ticketsService.search(input);
    }

    @Query(() => TicketEntity, { name: 'tickets_detail' })
    async ticketDetail(@Args('id', { type: () => Int }) id: number): Promise<TicketEntity> {
        const ticket = await this.ticketsService.findOne(id);
        if (!ticket) {
            throw new NotFoundException('Ticket not found');
        }
        return ticket;
    }

    @Mutation(() => TicketEntity, { name: 'tickets_create' })
    async createTicket(
        @Args('input') input: TicketCreateInputDto,
        @AuthUser() user: UserEntity
    ): Promise<TicketEntity> {
        return this.ticketsService.create({
            ...input,
            status_id: TicketStatus.PENDING,
            created_by: user.id,
        });
    }

    @Mutation(() => TicketEntity, { name: 'tickets_update' })
    async updateTicket(
        @Args('id', { type: () => Int }) id: number,
        @Args('input') input: TicketUpdateInputDto,
        @AuthUser() user: UserEntity
    ): Promise<TicketEntity> {
        const ticket = await this.ticketsService.findOne(id);
        if (!ticket) {
            throw new NotFoundException('Ticket not found');
        }

        return this.ticketsService.updateOne(id, {
            ...input,
            updated_by: user.id,
        });
    }

    @Mutation(() => Boolean, { name: 'tickets_delete' })
    async deleteTicket(@Args('id', { type: () => Int }) id: number, @AuthUser() user: UserEntity): Promise<Boolean> {
        const ticket = await this.ticketsService.findOne(id);
        if (!ticket) {
            throw new NotFoundException('Ticket not found');
        }

        await this.ticketsService.softDelete(id, user.id);
        return true;
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async customer(@Parent() ticket: TicketEntity): Promise<UserEntity | null> {
        return this.dataLoader.relationBatchOne(UserEntity).load(ticket.customer_id);
    }

    @ResolveField(() => WebsiteEntity, { nullable: true })
    async website(@Parent() ticket: TicketEntity): Promise<WebsiteEntity | null> {
        if (!ticket.website_id) return null;
        return this.dataLoader.relationBatchOne(WebsiteEntity).load(ticket.website_id);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async createdByUser(@Parent() ticket: TicketEntity): Promise<UserEntity | null> {
        if (!ticket.created_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(ticket.created_by);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async updatedByUser(@Parent() ticket: TicketEntity): Promise<UserEntity | null> {
        if (!ticket.updated_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(ticket.updated_by);
    }
}
