import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { TicketType } from '../../../entities/ticket.entity';
import { IdExists } from '../../../commons/validators/id-exists.validator';

@InputType()
export class TicketCreateInputDto {
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists('users')
    @Field(() => Int)
    customer_id: number;

    @IsOptional()
    @IdExists('websites')
    @Field(() => Int, { nullable: true })
    website_id?: number;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @Field()
    title: string;

    @IsOptional()
    @IsString()
    @Field({ nullable: true })
    content?: string;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(TicketType)
    @Field(() => Int)
    type_id: TicketType;
}
