import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { TicketType, TicketStatus } from '../../../entities/ticket.entity';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';

@InputType()
export class TicketUpdateInputDto extends BaseUpdateInputDto {
    @IsOptional()
    @IdExists('users')
    @Field(() => Int, { nullable: true })
    customer_id?: number;

    @IsOptional()
    @IdExists('websites')
    @Field(() => Int, { nullable: true })
    website_id?: number;

    @IsOptional()
    @IsString()
    @Field({ nullable: true })
    title?: string;

    @IsOptional()
    @IsString()
    @Field({ nullable: true })
    content?: string;

    @IsOptional()
    @IsString()
    @Field({ nullable: true })
    feedback?: string;

    @IsOptional()
    @IsEnum(TicketType)
    @Field(() => Int, { nullable: true })
    type_id?: TicketType;

    @IsOptional()
    @IsEnum(TicketStatus)
    @Field(() => Int, { nullable: true })
    status_id?: TicketStatus;
}
