import { Injectable } from '@nestjs/common';
import { BaseService } from '../../commons/bases/base.service';
import { IndustryEntity } from '../../entities/industry.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class IndustriesService extends BaseService<IndustryEntity> {
    constructor(
        @InjectRepository(IndustryEntity)
        public readonly repo: Repository<IndustryEntity>
    ) {
        super(repo);
    }
}
