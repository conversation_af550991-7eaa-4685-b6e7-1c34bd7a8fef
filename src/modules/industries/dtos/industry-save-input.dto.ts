import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsInt, IsNotEmpty, IsPositive, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { ItemStatus } from '../../../commons/enums.common';

@InputType()
export class IndustrySaveInputDto {
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @Field()
    name: string;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @IsPositive()
    @Field(() => Int)
    display_order: number;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(ItemStatus)
    @Field(() => Int)
    status_id: ItemStatus;
}
