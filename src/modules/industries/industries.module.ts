import { Module } from '@nestjs/common';
import { IndustriesService } from './industries.service';
import { IndustriesResolver } from './industries.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IndustryEntity } from '../../entities/industry.entity';

@Module({
    imports: [TypeOrmModule.forFeature([IndustryEntity])],
    providers: [IndustriesService, IndustriesResolver],
})
export class IndustriesModule {}
