import { Args, Int, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { IndustryEntity } from '../../entities/industry.entity';
import { IndustriesService } from './industries.service';
import { UserEntity, UserRoles } from '../../entities/user.entity';
import { DataLoaderService } from '../../data-loaders/data-loaders.service';
import { BaseInput } from '../../commons/bases/base.input';
import { AuthUser } from '../auth/auth.decorator';
import { IndustrySaveInputDto } from './dtos/industry-save-input.dto';
import { AuthMutation } from '../../commons/decorators/graphql.decorators';
import { Roles } from '../../commons/decorators/roles.decorator';
import { TemplateEntity } from '../../entities/template.entity';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { ItemStatus } from '../../commons/enums.common';

@Resolver(IndustryEntity)
export class IndustriesResolver {
    constructor(
        private readonly industriesService: IndustriesService,
        private readonly dataLoaderService: DataLoaderService
    ) {}

    @ResolveField(() => [UserEntity], { nullable: true })
    async users(@Parent() industry: IndustryEntity): Promise<UserEntity[]> {
        return this.dataLoaderService.relationBatchOneMany(UserEntity, 'users').load(industry.id);
    }

    @ResolveField(() => [TemplateEntity], { nullable: true })
    async templates(@Parent() industry: IndustryEntity): Promise<TemplateEntity[]> {
        return this.dataLoaderService.relationBatchManyMany(TemplateEntity, 'templates').load(industry.id);
    }

    @Query(() => [IndustryEntity], { name: 'industry_list' })
    async industries(@Args('body') body: BaseInput): Promise<IndustryEntity[]> {
        return this.industriesService.findAllBy(body);
    }

    @AuthMutation(() => IndustryEntity, { name: 'industry_create' })
    @Roles(UserRoles.ADMIN)
    async store(@Args('body') body: IndustrySaveInputDto, @AuthUser() auth: UserEntity): Promise<IndustryEntity> {
        return this.industriesService.create({ ...body, created_by: auth.id });
    }

    @AuthMutation(() => IndustryEntity, { name: 'industry_update' })
    @Roles(UserRoles.ADMIN)
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: IndustrySaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<IndustryEntity> {
        return this.industriesService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @AuthMutation(() => Boolean, { name: 'industry_delete' })
    @Roles(UserRoles.ADMIN)
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<Boolean> {
        // Check if industry exists and get its status
        const industry = await this.industriesService.findOne(id);
        if (!industry) {
            throw new NotFoundException('Industry not found');
        }

        // Check if industry has active status
        if (industry.status_id === ItemStatus.ACTIVE) {
            throw new BadRequestException('Cannot delete industry with active status');
        }

        await this.industriesService.softDelete(id, auth.id);
        return true;
    }
}
