import { Args, Mutation } from '@nestjs/graphql';
import { UserRoles } from '../../entities/user.entity';
import { AuthResolver } from '../../commons/decorators/graphql.decorators';
import { Roles } from '../../commons/decorators/roles.decorator';
import { IPaymentIntentResponse, PaymentService } from './payment.service';
import { PaymentIntentInputDto } from './dtos/PaymentIntentInput.dto';
import { PaymentIntentResponseModel } from './models/payment-intent-response.model';

@AuthResolver()
@Roles(UserRoles.CUSTOMER)
export class PaymentResolver {
    constructor(private readonly paymentService: PaymentService) {}

    @Mutation(() => PaymentIntentResponseModel, { name: 'create_payment_intent' })
    async createPaymentIntent(@Args('body') body: PaymentIntentInputDto): Promise<IPaymentIntentResponse> {
        return this.paymentService.createPaymentIntent(body.amount, body.currency);
    }
}
