import { Injectable } from '@nestjs/common';
import Strip<PERSON> from 'stripe';
import appConf from '../../configs/app.conf';

export interface IPaymentIntentResponse {
    success: boolean;
    client_secret?: string | null;
    error?: string;
}

@Injectable()
export class PaymentService {
    private stripe: Stripe;

    constructor() {
        this.stripe = new Stripe(appConf.STRIPE_SECRET_KEY, {
            apiVersion: '2025-05-28.basil' as Stripe.LatestApiVersion,
        });
    }

    async createPaymentIntent(amount: number, currency: string, customerId?: string): Promise<IPaymentIntentResponse> {
        try {
            const paymentIntent = await this.stripe.paymentIntents.create({
                amount,
                currency,
                customer: customerId, // (Tùy chọn) ID của khách hàng
                // payment_method_types: ['card'], // Loại thanh toán
            });

            return {
                success: true,
                client_secret: paymentIntent.client_secret, // <PERSON><PERSON><PERSON> về frontend để xác thực
            };
        } catch (error) {
            console.error('Error creating payment intent:', error);
            return { success: false, error: error.message };
        }
    }
}
