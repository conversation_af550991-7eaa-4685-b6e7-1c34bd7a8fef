import { IsInt, IsISO4217<PERSON><PERSON><PERSON>cyCode, IsNotEmpty, IsPositive } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { Field, InputType } from '@nestjs/graphql';

@InputType()
export class PaymentIntentInputDto {
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @IsPositive()
    @Field(() => Number)
    amount: number;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsISO4217CurrencyCode()
    @Field(() => String)
    currency: string;
}
