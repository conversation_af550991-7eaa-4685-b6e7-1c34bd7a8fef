import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseService } from '../../commons/bases/base.service';
import { SmtpEntity } from 'src/entities/smtp.entity';

@Injectable()
export class SmtpService extends BaseService<SmtpEntity> {
    constructor(
        @InjectRepository(SmtpEntity)
        public readonly repo: Repository<SmtpEntity>
    ) {
        super(repo);
    }
}
