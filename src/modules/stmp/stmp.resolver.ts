import { Args, Int, <PERSON><PERSON>, Query, Resolve<PERSON>ield, Resolver } from '@nestjs/graphql';
import { SmtpEntity } from '../../entities/smtp.entity';
import { UserEntity, UserRoles } from '../../entities/user.entity';
import { AuthMutation, AuthResolver } from '../../commons/decorators/graphql.decorators';
import { AuthUser } from '../auth/auth.decorator';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { IPaginatedType } from '../../commons/bases/base.model';
import { Roles } from '../../commons/decorators/roles.decorator';
import { DataLoaderService } from '../../data-loaders/data-loaders.service';
import { SmtpModel } from './models/stmp.model';
import { SmtpSaveInputDto } from './dtos/stmp-save-input.dto';
import { SmtpService } from './stmp.service';

@AuthResolver(SmtpEntity)
@Roles(UserRoles.ADMIN)
export class SmtpResolver {
    constructor(
        private readonly smtpService: SmtpService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => UserEntity, { nullable: true })
    async customer(@Parent() smtp: SmtpEntity): Promise<UserEntity | null> {
        if (!smtp.customer_id) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(smtp.customer_id);
    }

    @Query(() => SmtpModel, { name: 'smtps_list' })
    async index(@Args('input') input: BasePaginationInput): Promise<IPaginatedType<SmtpEntity>> {
        return this.smtpService.search(input);
    }

    @AuthMutation(() => SmtpEntity, { name: 'smtps_create' })
    async create(@Args('input') input: SmtpSaveInputDto, @AuthUser() auth: UserEntity): Promise<SmtpEntity> {
        return this.smtpService.create({
            ...input,
            created_by: auth.id,
        });
    }

    @AuthMutation(() => SmtpEntity, { name: 'smtps_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('input') input: SmtpSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<SmtpEntity> {
        return this.smtpService.updateOne(id, {
            ...input,
            updated_by: auth.id,
        });
    }

    @AuthMutation(() => Boolean, { name: 'smtps_delete' })
    async delete(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.smtpService.softDelete(id, auth.id);
        return true;
    }
}
