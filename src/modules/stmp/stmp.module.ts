import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SmtpEntity } from 'src/entities/smtp.entity';
import { SmtpResolver } from './stmp.resolver';
import { SmtpService } from './stmp.service';

@Module({
    imports: [TypeOrmModule.forFeature([SmtpEntity])],
    providers: [SmtpService, SmtpResolver],
    exports: [SmtpService],
})
export class SmtpModule {}
