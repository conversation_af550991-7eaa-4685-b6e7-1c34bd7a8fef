import { Field, InputType } from '@nestjs/graphql';
import { IsBoolean, IsNotEmpty, IsNumber, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

@InputType()
export class SmtpConfigInputDto {
    @Field()
    @IsString()
    @IsNotEmpty()
    host: string;

    @Field()
    @IsNumber()
    @IsNotEmpty()
    port: number;

    @Field()
    @IsBoolean()
    ssl: boolean;

    @Field()
    @IsString()
    @IsNotEmpty()
    username: string;

    @Field()
    @IsString()
    @IsNotEmpty()
    password: string;
}

@InputType()
export class SmtpSaveInputDto {
    @Field()
    @IsNumber()
    @IsNotEmpty()
    customer_id: number;

    @Field(() => SmtpConfigInputDto)
    @ValidateNested()
    @Type(() => SmtpConfigInputDto)
    @IsNotEmpty()
    smtp_config: SmtpConfigInputDto;
}
