import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BackupWebsiteEntity } from '../../entities/backup-website.entity';
import { BackupWebsitesService } from './backup-websites.service';
import { BackupWebsitesResolver } from './backup-websites.resolver';

@Module({
    imports: [TypeOrmModule.forFeature([BackupWebsiteEntity])],
    providers: [BackupWebsitesService, BackupWebsitesResolver],
    exports: [BackupWebsitesService],
})
export class BackupWebsitesModule {}
