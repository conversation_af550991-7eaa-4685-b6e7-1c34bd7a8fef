import { Args, Int, Mutation, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { BadRequestException, NotFoundException, UseGuards } from '@nestjs/common';
import { BackupWebsiteEntity } from '../../entities/backup-website.entity';
import { BackupWebsitesService } from './backup-websites.service';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { BackupWebsiteModel } from './models/backup-website.model';
import { IPaginatedType } from '../../commons/bases/base.model';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { Roles } from '../../commons/decorators/roles.decorator';
import { UserRoles, UserEntity } from '../../entities/user.entity';
import { WebsiteEntity } from '../../entities/website.entity';
import { FileEntity } from '../../entities/file.entity';
import { DataLoaderService } from '../../data-loaders/data-loaders.service';
import { AuthUser } from '../auth/auth.decorator';

@Resolver(() => BackupWebsiteEntity)
@UseGuards(JwtAuthGuard)
@Roles(UserRoles.ADMIN)
export class BackupWebsitesResolver {
    constructor(
        private readonly backupWebsitesService: BackupWebsitesService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @Query(() => BackupWebsiteModel, { name: 'backup_websites_list' })
    async backupWebsites(@Args('input') input: BasePaginationInput): Promise<IPaginatedType<BackupWebsiteEntity>> {
        return this.backupWebsitesService.search(input);
    }

    @Mutation(() => BackupWebsiteEntity, { name: 'backup_websites_update_restore_status' })
    async updateRestoreStatus(
        @Args('id', { type: () => Int }) id: number,
        @AuthUser() user: UserEntity
    ): Promise<BackupWebsiteEntity> {
        const backupWebsite = await this.backupWebsitesService.findOne(id);
        if (!backupWebsite) {
            throw new NotFoundException('Backup website not found');
        }
        if (backupWebsite.is_process_restore) {
            throw new BadRequestException('This backup website is already in the process of restoring');
        }

        await this.backupWebsitesService.updateOne(id, {
            is_process_restore: true,
            updated_by: user.id,
        });

        // Return the updated entity with the new values
        return {
            ...backupWebsite,
            is_process_restore: true,
            updated_by: user.id,
            updated_at: new Date(),
        };
    }

    @ResolveField(() => WebsiteEntity, { nullable: true })
    async website(@Parent() backupWebsite: BackupWebsiteEntity): Promise<WebsiteEntity | null> {
        return this.dataLoader.relationBatchOne(WebsiteEntity).load(backupWebsite.website_id);
    }

    @ResolveField(() => FileEntity, { nullable: true })
    async file(@Parent() backupWebsite: BackupWebsiteEntity): Promise<FileEntity | null> {
        if (!backupWebsite.file_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(backupWebsite.file_id);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async createdByUser(@Parent() backupWebsite: BackupWebsiteEntity): Promise<UserEntity | null> {
        if (!backupWebsite.created_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(backupWebsite.created_by);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async updatedByUser(@Parent() backupWebsite: BackupWebsiteEntity): Promise<UserEntity | null> {
        if (!backupWebsite.updated_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(backupWebsite.updated_by);
    }
}
