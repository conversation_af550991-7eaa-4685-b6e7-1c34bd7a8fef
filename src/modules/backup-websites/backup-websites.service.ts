import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BackupWebsiteEntity } from '../../entities/backup-website.entity';
import { BaseService } from '../../commons/bases/base.service';

@Injectable()
export class BackupWebsitesService extends BaseService<BackupWebsiteEntity> {
    constructor(
        @InjectRepository(BackupWebsiteEntity)
        public readonly repo: Repository<BackupWebsiteEntity>
    ) {
        super(repo);
    }
}
