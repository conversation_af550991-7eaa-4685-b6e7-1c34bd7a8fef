import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { SubscriptionPlanEntity } from '../../../entities/subscription-plan.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class SubscriptionPlansService extends BaseService<SubscriptionPlanEntity> {
    constructor(@InjectRepository(SubscriptionPlanEntity) private readonly repo: Repository<SubscriptionPlanEntity>) {
        super(repo);
    }
}
