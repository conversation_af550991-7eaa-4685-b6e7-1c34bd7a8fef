import { Module } from '@nestjs/common';
import { SubscriptionPlansService } from './services/subscription-plans.service';
import { SubscriptionPlansResolver } from './resolvers/subscription-plans.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SubscriptionPlanEntity } from '../../entities/subscription-plan.entity';

@Module({
    imports: [TypeOrmModule.forFeature([SubscriptionPlanEntity])],
    providers: [SubscriptionPlansService, SubscriptionPlansResolver],
})
export class SubscriptionsModule {}
