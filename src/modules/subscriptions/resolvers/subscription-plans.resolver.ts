import { Args, Int, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { UserEntity, UserRoles } from '../../../entities/user.entity';
import { BaseInput } from '../../../commons/bases/base.input';
import { AuthUser } from '../../auth/auth.decorator';
import { AuthMutation } from '../../../commons/decorators/graphql.decorators';
import { Roles } from '../../../commons/decorators/roles.decorator';
import { SubscriptionPlanEntity } from '../../../entities/subscription-plan.entity';
import { SubscriptionPlansService } from '../services/subscription-plans.service';
import { SubscriptionPlanSaveInputDto } from '../dtos/subscription-plan-save-input.dto';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { ItemStatus } from '../../../commons/enums.common';

@Resolver(SubscriptionPlanEntity)
export class SubscriptionPlansResolver {
    constructor(private readonly subscriptionPlansService: SubscriptionPlansService) {}

    @Query(() => [SubscriptionPlanEntity], { name: 'subscription_plan_list' })
    async index(@Args('body') body: BaseInput): Promise<SubscriptionPlanEntity[]> {
        return this.subscriptionPlansService.findAllBy(body);
    }

    @Query(() => SubscriptionPlanEntity, { name: 'subscription_plan_view' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<SubscriptionPlanEntity> {
        return this.subscriptionPlansService.findOne(id).then((sp) => {
            if (!sp) throw new NotFoundException();
            return sp;
        });
    }

    @AuthMutation(() => SubscriptionPlanEntity, { name: 'subscription_plan_create' })
    @Roles(UserRoles.ADMIN)
    async store(
        @Args('body') body: SubscriptionPlanSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<SubscriptionPlanEntity> {
        return this.subscriptionPlansService.create({ ...body, created_by: auth.id });
    }

    @AuthMutation(() => SubscriptionPlanEntity, { name: 'subscription_plan_update' })
    @Roles(UserRoles.ADMIN)
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: SubscriptionPlanSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<SubscriptionPlanEntity> {
        return this.subscriptionPlansService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @AuthMutation(() => Boolean, { name: 'subscription_plan_delete' })
    @Roles(UserRoles.ADMIN)
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<Boolean> {
        // Check if subscription plan exists and get its status
        const subscriptionPlan = await this.subscriptionPlansService.findOne(id);
        if (!subscriptionPlan) {
            throw new NotFoundException('Subscription plan not found');
        }

        // Check if subscription plan has active status
        if (subscriptionPlan.status_id === ItemStatus.ACTIVE) {
            throw new BadRequestException('Cannot delete subscription plan with active status');
        }

        await this.subscriptionPlansService.softDelete(id, auth.id);
        return true;
    }
}
