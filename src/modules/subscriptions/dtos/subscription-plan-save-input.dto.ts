import { Field, Float, InputType, Int } from '@nestjs/graphql';
import {
    IsArray,
    IsEnum,
    IsInt,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsPositive,
    IsString,
    Max,
    Min,
    ValidateNested,
} from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { ItemStatus } from '../../../commons/enums.common';
import { Type } from 'class-transformer';
import { MatchNumber } from '../../../commons/validators/match-number.validator';
import { PlanType } from '../../../entities/subscription-plan.entity';
import { GraphQLJSON } from 'graphql-type-json';

@InputType()
export class SubscriptionPlanSaveInputDto {
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @Field()
    name: string;

    @IsOptional()
    @IsString()
    @Field()
    desc: string;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(ItemStatus)
    @Field(() => Int)
    status_id: ItemStatus;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsNumber({})
    @Min(0)
    @Max(999999.99)
    @MatchNumber(8, 2)
    @Field(() => Float)
    price: number;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @IsPositive()
    @Field(() => Int)
    display_order: number;

    @IsOptional()
    @IsArray()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED, each: true })
    @ValidateNested({ each: true })
    @Type(() => FeatureInput)
    @Field(() => [FeatureInput], { nullable: true })
    features?: FeatureInput[];

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(PlanType)
    @Field(() => Int)
    type_id: PlanType;

    @IsOptional()
    @IsInt()
    @Min(0)
    @Field(() => Int, { nullable: true })
    trial_days?: number;

    @IsOptional()
    @IsNumber({})
    @Min(0)
    @Max(999999.99)
    @MatchNumber(8, 2)
    @Field(() => Float, { nullable: true })
    original_price?: number;

    @IsOptional()
    @IsString()
    @Field({ nullable: true })
    sub_desc?: string;

    @IsOptional()
    @Field(() => GraphQLJSON, { nullable: true })
    highlights?: any;

    @IsOptional()
    @IsString()
    @Field({ nullable: true })
    woo_subscription_id?: string;
}

@InputType()
export class FeatureInput {
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @IsPositive()
    @Field(() => Int)
    id: number;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @Field()
    value: string;
}
