import { Injectable } from '@nestjs/common';
import { ConfigsService } from './configs.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigEntity } from '../../entities/config.entity';

@Injectable()
export class ConfigsSeederService {
    constructor(
        @InjectRepository(ConfigEntity)
        private readonly configRepo: Repository<ConfigEntity>,
        private readonly configsService: ConfigsService
    ) {}

    async seedInitialConfigs(): Promise<void> {
        const initialConfigs = [
            {
                code: 'app_name',
                name: 'Application Name',
                value: 'IPT API System',
            },
            {
                code: 'admin_email',
                name: 'Administrator Email',
                value: '<EMAIL>',
            },
            {
                code: 'site_maintenance',
                name: 'Site Maintenance Mode',
                value: 'false',
            },
            {
                code: 'max_file_size',
                name: 'Maximum File Upload Size (MB)',
                value: '10',
            },
            {
                code: 'backup_retention_days',
                name: 'Backup Retention Days',
                value: '30',
            },
            {
                code: 'trial_period_days',
                name: 'Trial Period Days',
                value: '3',
            },
            {
                code: 'max_wordpress_instances',
                name: 'Maximum WordPress Instances',
                value: '1000',
            },
            {
                code: 'wordpress_instance_threshold',
                name: 'WordPress Instance Usage Threshold (%)',
                value: '75',
            },
            {
                code: 'daily_backup_enabled',
                name: 'Daily Backup Enabled',
                value: 'true',
            },
            {
                code: 'customer_backup_frequency',
                name: 'Customer Backup Frequency (days)',
                value: '3',
            },
            {
                code: 'customer_backup_retention',
                name: 'Customer Backup Retention Count',
                value: '10',
            },
        ];

        for (const configData of initialConfigs) {
            const existingConfig = await this.configsService.getByCode(configData.code);

            if (!existingConfig) {
                await this.configRepo.save({
                    ...configData,
                    created_at: new Date(),
                });
                console.log(`✅ Seeded config: ${configData.code}`);
            } else {
                console.log(`⚠️  Config already exists: ${configData.code}`);
            }
        }
    }

    async seedConfigsFromFeatureList(): Promise<void> {
        // Based on your feature list image, here are the configurations
        const featureConfigs = [
            {
                code: 'website_design_basic_premium',
                name: 'Website Designs - Basic/Premium Trial',
                value: 'premium_required',
            },
            {
                code: 'trial_account_deletion_days',
                name: 'Trial Account Deletion Period (days)',
                value: '3',
            },
            {
                code: 'backup_admin_daily',
                name: 'Admin Daily Backup',
                value: 'true',
            },
            {
                code: 'backup_admin_count',
                name: 'Admin Backup Count',
                value: '30',
            },
            {
                code: 'backup_customer_frequency_days',
                name: 'Customer Backup Frequency (days)',
                value: '3',
            },
            {
                code: 'backup_customer_count',
                name: 'Customer Backup Count',
                value: '10',
            },
            {
                code: 'wp_instance_usage_threshold',
                name: 'WordPress Instance Usage Threshold (%)',
                value: '75',
            },
            {
                code: 'wp_instance_provision_extra',
                name: 'Extra WordPress Instances to Provision',
                value: '100',
            },
            {
                code: 'wp_version_update_notification',
                name: 'WordPress Version Update Notification',
                value: 'true',
            },
        ];

        for (const configData of featureConfigs) {
            const existingConfig = await this.configsService.getByCode(configData.code);

            if (!existingConfig) {
                await this.configRepo.save({
                    ...configData,
                    created_at: new Date(),
                });
                console.log(`✅ Seeded feature config: ${configData.code}`);
            }
        }
    }
}
