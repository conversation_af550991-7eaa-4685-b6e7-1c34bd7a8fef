import { Field, InputType, IntersectionType } from '@nestjs/graphql';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';

@InputType()
export class ConfigUpdateInputDto extends BaseUpdateInputDto {
    @Field({ nullable: true })
    @IsOptional()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    value?: string;
}
