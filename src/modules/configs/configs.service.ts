import { Injectable, NotFoundException } from '@nestjs/common';
import { BaseService } from '../../commons/bases/base.service';
import { ConfigEntity } from '../../entities/config.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigUpdateInputDto } from './dtos/config-update-input.dto';
import { UserEntity } from '../../entities/user.entity';

@Injectable()
export class ConfigsService extends BaseService<ConfigEntity> {
    constructor(
        @InjectRepository(ConfigEntity)
        public readonly repo: Repository<ConfigEntity>
    ) {
        super(repo);
    }

    async update(id: number, body: ConfigUpdateInputDto, auth: UserEntity): Promise<ConfigEntity> {
        const config = await this.findOne(id);
        if (!config) {
            throw new NotFoundException('Config not found');
        }

        await this.repo.update(id, {
            ...body,
            updated_by: auth.id,
            updated_at: new Date(),
        });

        const updatedConfig = await this.findOne(id);
        if (!updatedConfig) {
            throw new NotFoundException('Config not found after update');
        }
        return updatedConfig;
    }

    async destroy(id: number, auth: UserEntity): Promise<boolean> {
        const config = await this.findOne(id);
        if (!config) {
            throw new NotFoundException('Config not found');
        }

        await this.repo.update(id, {
            deleted_by: auth.id,
            deleted_at: new Date(),
        });

        return true;
    }

    async getByCode(code: string): Promise<ConfigEntity | null> {
        return this.repo.findOne({ where: { code } });
    }
}
