import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigEntity } from '../../entities/config.entity';
import { ConfigsService } from './configs.service';
import { ConfigsResolver } from './configs.resolver';
import { ConfigsSeederService } from './configs-seeder.service';

@Module({
    imports: [TypeOrmModule.forFeature([ConfigEntity])],
    providers: [ConfigsService, ConfigsResolver, ConfigsSeederService],
    exports: [ConfigsService, ConfigsSeederService],
})
export class ConfigsModule {}
