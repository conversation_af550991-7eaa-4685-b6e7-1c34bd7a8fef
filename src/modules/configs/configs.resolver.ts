import { Args, Int, Mutation, Query } from '@nestjs/graphql';
import { ConfigsService } from './configs.service';
import { AuthResolver } from '../../commons/decorators/graphql.decorators';
import { ConfigsModel } from './models/configs.model';
import { ConfigEntity } from '../../entities/config.entity';
import { NotFoundException } from '@nestjs/common';
import { ConfigUpdateInputDto } from './dtos/config-update-input.dto';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { AuthUser } from '../auth/auth.decorator';
import { UserEntity, UserRoles } from '../../entities/user.entity';
import { Roles } from '../../commons/decorators/roles.decorator';
import { IPaginatedType } from '../../commons/bases/base.model';

@AuthResolver(ConfigEntity)
@Roles(UserRoles.ADMIN)
export class ConfigsResolver {
    constructor(private readonly configsService: ConfigsService) {}

    @Query(() => ConfigsModel, { name: 'configs_list' })
    async configs(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<ConfigEntity>> {
        return this.configsService.search(body);
    }

    @Query(() => ConfigEntity, { name: 'configs_detail' })
    async config(@Args('id', { type: () => Int }) id: number): Promise<ConfigEntity> {
        const config = await this.configsService.findOne(id);
        if (!config) {
            throw new NotFoundException('Config not found');
        }
        return config;
    }

    @Query(() => ConfigEntity, { name: 'configs_by_code', nullable: true })
    async configByCode(@Args('code') code: string): Promise<ConfigEntity | null> {
        return this.configsService.getByCode(code);
    }

    @Mutation(() => ConfigEntity, { name: 'configs_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ConfigUpdateInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ConfigEntity> {
        return this.configsService.update(id, body, auth);
    }
}
