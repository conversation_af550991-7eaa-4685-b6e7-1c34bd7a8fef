import { Args, Int, Mutation, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { WebsitesService } from './websites.service';
import { WebsiteEntity, WebsiteStatus } from '../../entities/website.entity';
import { WebsiteSaveInputDto } from './dtos/website-save-input.dto';
import { WebsiteUpdateStatusInputDto } from './dtos/website-update-status-input.dto';
import { WebsiteWpcliInputDto } from './dtos/website-wpcli-input.dto';
import { UserEntity, UserRoles } from '../../entities/user.entity';
import { AuthUser } from '../auth/auth.decorator';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { WebsiteModel } from './models/website.model';
import { TemplateEntity } from '../../entities/template.entity';
import { BadRequestException, NotFoundException, UseGuards } from '@nestjs/common';
import { DataLoaderService } from 'src/data-loaders/data-loaders.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TransactionEntity } from '../../entities/transaction.entity';
import { AuthMutation } from '../../commons/decorators/graphql.decorators';
import { IPaginatedType } from '../../commons/bases/base.model';
import { Roles } from '../../commons/decorators/roles.decorator';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BackupWebsiteEntity } from '../../entities/backup-website.entity';
import { ReviewsieuxeAxios } from '../../axios/reviewsieuxe.axios';
import { GraphQLJSON } from 'graphql-type-json';
import axios from 'axios';

@Resolver(() => WebsiteEntity)
@UseGuards(JwtAuthGuard)
@Roles(UserRoles.ADMIN)
export class WebsitesResolver {
    constructor(
        private readonly websitesService: WebsitesService,
        private readonly dataLoader: DataLoaderService,
        @InjectRepository(BackupWebsiteEntity)
        private readonly backupWebsiteRepo: Repository<BackupWebsiteEntity>,
        private readonly rsAxios: ReviewsieuxeAxios
    ) {}

    @Query(() => WebsiteModel, { name: 'websites_list' })
    async websites(@Args('input') input: BasePaginationInput): Promise<IPaginatedType<WebsiteEntity>> {
        return this.websitesService.search(input);
    }

    @AuthMutation(() => WebsiteEntity, { name: 'websites_create' })
    async createWebsite(
        @Args('input') input: WebsiteSaveInputDto,
        @AuthUser() user: UserEntity
    ): Promise<WebsiteEntity> {
        return this.websitesService.create({
            ...input,
            status_id: WebsiteStatus.WAITING,
            created_by: user.id,
        });
    }

    @AuthMutation(() => WebsiteEntity, { name: 'websites_update' })
    async updateWebsite(
        @Args('id', { type: () => Int }) id: number,
        @Args('input') input: WebsiteSaveInputDto,
        @AuthUser() user: UserEntity
    ): Promise<WebsiteEntity> {
        return this.websitesService.updateOne(id, {
            ...input,
            updated_by: user.id,
        });
    }

    @AuthMutation(() => WebsiteEntity, { name: 'websites_update_status' })
    async updateWebsiteStatus(
        @Args('id', { type: () => Int }) id: number,
        @Args('input') input: WebsiteUpdateStatusInputDto,
        @AuthUser() user: UserEntity
    ): Promise<WebsiteEntity> {
        const website = await this.websitesService.findOne(id);
        if (!website) {
            throw new NotFoundException('Website not found');
        }

        // Update the website status in database first
        const updatedWebsite = await this.websitesService.updateOne(id, {
            status_id: input.status_id,
            updated_by: user.id,
        });

        // Call external API based on status_id
        const domain = website.custom_domain || website.domain;
        if (domain) {
            try {
                if (input.status_id === 4) { // Active status
                    await axios.post(
                        `https://${domain}/wp-json/customer-edit/v1/site-status`,
                        { action: 'enable' },
                        {
                            headers: {
                                'Authorization': 'Bearer ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys',
                                'Content-Type': 'application/json'
                            },
                            timeout: 30000
                        }
                    );
                } else if (input.status_id === 7) { // Disabled status
                    await axios.post(
                        `https://${domain}/wp-json/customer-edit/v1/site-status`,
                        { action: 'disable' },
                        {
                            headers: {
                                'Authorization': 'Bearer ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys',
                                'Content-Type': 'application/json'
                            },
                            timeout: 30000
                        }
                    );
                }
            } catch (error) {
                // Log the error but don't fail the mutation
                console.error(`Failed to update site status for domain ${domain}:`, error.message);
            }
        }

        return updatedWebsite;
    }


    @AuthMutation(() => BackupWebsiteEntity, { name: 'websites_request_backup' })
    async requestBackup(
        @Args('id', { type: () => Int }) id: number,
        @AuthUser() user: UserEntity
    ): Promise<BackupWebsiteEntity> {
        // Check if website exists
        const website = await this.websitesService.findOne(id);
        if (!website) {
            throw new NotFoundException('Website not found');
        }

        // Check if backup is already in process
        if (website.is_process_backup) {
            throw new BadRequestException('Backup is already in process for this website');
        }

        // Step 1: Update is_process_backup to true
        await this.websitesService.updateOne(id, {
            is_process_backup: true,
            updated_by: user.id,
        });

        // Step 2: Create a new record in backup_websites with file_id = NULL
        const backupWebsite = await this.backupWebsiteRepo.save({
            website_id: id,
            file_id: undefined,
            is_request: false,
            created_by: user.id,
        });

        return backupWebsite;
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async customer(@Parent() website: WebsiteEntity): Promise<UserEntity | null> {
        if (!website.customer_id) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(website.customer_id);
    }

    @ResolveField(() => TemplateEntity, { nullable: true })
    async template(@Parent() website: WebsiteEntity): Promise<TemplateEntity | null> {
        if (!website.template_id) return null;
        return this.dataLoader.relationBatchOne(TemplateEntity).load(website.template_id);
    }

    @ResolveField(() => [TransactionEntity], { nullable: true })
    async transactions(@Parent() website: WebsiteEntity): Promise<TransactionEntity[] | null> {
        if (!website.id) return null;
        return this.dataLoader.relationBatchOneMany(TransactionEntity, 'website').load(website.id);
    }

    @AuthMutation(() => GraphQLJSON, { name: 'websites_wpcli' })
    async websiteWpcli(@Args('input') input: WebsiteWpcliInputDto, @AuthUser() user: UserEntity): Promise<any> {
        return this.rsAxios.wpcliAsync(input.domain, input.command);
    }

    @Query(() => GraphQLJSON, { name: 'websites_health_check' })
    async websiteHealthCheck(
        @Args('id', { type: () => Int }) id: number,
        @AuthUser() user: UserEntity
    ): Promise<any> {
        // Get the website by ID
        const website = await this.websitesService.findOne(id);
        if (!website) {
            throw new NotFoundException('Website not found');
        }

        // Determine the domain to use
        const domain = website.custom_domain || website.domain;
        if (!domain) {
            throw new BadRequestException('Website domain not found');
        }

        try {
            // Make request to the site-status API
            const response = await axios.get(
                `https://${domain}/wp-json/customer-edit/v1/site-status`,
                {
                    timeout: 30000, // 30 seconds timeout
                }
            );

            // API response format: { status: true, disabled: false, message: "Site is active", is_custom_domain: false }
            const apiData = response.data;

            return {
                success: apiData.status,
                domain: domain,
                disabled: apiData.disabled,
                message: apiData.message,
                is_custom_domain: apiData.is_custom_domain,
            };
        } catch (error) {
            return {
                success: false,
                domain: domain,
                disabled: null,
                message: error.message,
                is_custom_domain: null,
            };
        }
    }
}
