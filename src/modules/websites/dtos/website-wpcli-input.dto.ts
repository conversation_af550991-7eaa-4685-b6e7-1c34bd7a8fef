import { Field, InputType } from '@nestjs/graphql';
import { IsNotEmpty, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';

@InputType()
export class WebsiteWpcliInputDto {
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @Field()
    domain: string;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @Field()
    command: string;
}
