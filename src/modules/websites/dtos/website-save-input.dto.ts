import { Field, InputType, Int } from '@nestjs/graphql';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { UserEntity } from '../../../entities/user.entity';
import { TemplateEntity } from '../../../entities/template.entity';
import Graph<PERSON>JSON from 'graphql-type-json';

@InputType()
export class WebsiteSaveInputDto {
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(UserEntity)
    @Field(() => Int)
    customer_id: number;

    @IsOptional()
    @IdExists(TemplateEntity)
    @Field(() => Int, { nullable: true })
    template_id?: number;

    @IsOptional()
    @IsString()
    @Field(() => String, { nullable: true })
    domain?: string;

    @IsOptional()
    @IsString()
    @Field(() => String, { nullable: true })
    custom_domain?: string;

    @IsOptional()
    @Field(() => GraphQLJSON, { nullable: true })
    info?: any;

    @IsOptional()
    @IsBoolean()
    @Field({ nullable: true, defaultValue: false })
    is_process_backup?: boolean;
}

export interface IWebsiteSaveInput {
    customer_id?: number;
    template_id?: number;
    domain?: string;
    custom_domain?: string;
    subscription_id?: number;
    is_process_backup?: boolean;
    created_by?: number;
    updated_by?: number;
}
