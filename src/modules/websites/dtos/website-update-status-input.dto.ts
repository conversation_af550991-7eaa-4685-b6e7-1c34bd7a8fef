import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { WebsiteStatus } from '../../../entities/website.entity';

@InputType()
export class WebsiteUpdateStatusInputDto {
    @Field(() => Int)
    @IsEnum(WebsiteStatus, { message: 'Invalid website status' })
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    status_id: WebsiteStatus;
}
