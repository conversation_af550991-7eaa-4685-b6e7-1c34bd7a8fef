import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WebsiteEntity } from '../../entities/website.entity';
import { BaseService } from '../../commons/bases/base.service';

@Injectable()
export class WebsitesService extends BaseService<WebsiteEntity> {
    constructor(
        @InjectRepository(WebsiteEntity)
        public readonly repo: Repository<WebsiteEntity>
    ) {
        super(repo);
    }
}
