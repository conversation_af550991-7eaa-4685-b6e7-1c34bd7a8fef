import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WebsiteEntity } from '../../entities/website.entity';
import { BackupWebsiteEntity } from '../../entities/backup-website.entity';
import { WebsitesService } from './websites.service';
import { WebsitesResolver } from './websites.resolver';
import { AxiosModule } from '../../axios/axios.module';

@Module({
    imports: [TypeOrmModule.forFeature([WebsiteEntity, BackupWebsiteEntity]), AxiosModule],
    providers: [WebsitesService, WebsitesResolver],
    exports: [WebsitesService],
})
export class WebsitesModule {}
