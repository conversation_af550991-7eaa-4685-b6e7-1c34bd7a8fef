import { MiddlewareConsumer, <PERSON><PERSON><PERSON>, RequestMethod } from '@nestjs/common';
import { FilesService } from './files.service';
import { FilesController } from './files.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MulterModule } from '@nestjs/platform-express';
import { FileEntity } from '../../entities/file.entity';
import { multerConfig } from '../../configs/multer.conf';
import { CleanupUploadMiddleware } from '../../commons/middlewares/cleanup-upload.middleware';

@Module({
    imports: [
        TypeOrmModule.forFeature([FileEntity]),
        MulterModule.register({
            dest: multerConfig.dest,
        }),
    ],
    providers: [FilesService],
    controllers: [FilesController],
})
export class FilesModule {
    configure(consumer: MiddlewareConsumer) {
        consumer.apply(CleanupUploadMiddleware).forRoutes({ path: '*', method: RequestMethod.POST });
    }
}
