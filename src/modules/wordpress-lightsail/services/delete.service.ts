import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { StatusService } from './status.service';
import { LightsailClient, DeleteInstanceCommand, DeleteKeyPairCommand } from '@aws-sdk/client-lightsail';
import * as AWS from 'aws-sdk';
import axios from 'axios';

@Injectable()
export class DeleteService {
    private readonly lightsail: LightsailClient;
    private readonly s3: AWS.S3;
    private readonly SSH_KEY_BUCKET: string;
    private readonly AWS_REGION: string;

    constructor(
        private readonly configService: ConfigService,
        private readonly statusService: StatusService
    ) {
        this.AWS_REGION = this.configService.get<string>('AWS_REGION', 'ap-southeast-1');
        this.SSH_KEY_BUCKET = this.configService.get<string>('SSH_KEY_BUCKET', '');
        this.lightsail = new LightsailClient({ region: this.AWS_REGION });
        this.s3 = new AWS.S3();
    }

    async deleteResources(domain_name: string) {
        if (!domain_name) throw new Error('Missing domain_name');

        const instanceName = domain_name.replace(/\./g, '-');
        const keyName = `${instanceName}-key`;

        const steps: Array<{ step: string; success: boolean; message?: string }> = [];

        try {
            await this.lightsail.send(new DeleteInstanceCommand({ instanceName }));
            await this.statusService.updateStatus(instanceName, 'deleted_instance');
            console.log(`🗑️ Deleted Lightsail instance: ${instanceName}`);
            steps.push({ step: 'deleted_instance', success: true });
        } catch (err) {
            console.warn(`⚠️ Failed to delete instance: ${err.message}`);
            steps.push({ step: 'deleted_instance', success: false, message: err.message });
        }

        try {
            const ZONE_ID = this.configService.get<string>('CF_ZONE_ID');
            const CF_TOKEN = this.configService.get<string>('CF_API_TOKEN');
            const cfRecordURL = `https://api.cloudflare.com/client/v4/zones/${ZONE_ID}/dns_records`;

            const { data } = await axios.get(cfRecordURL, {
                headers: { Authorization: `Bearer ${CF_TOKEN}` },
                params: { type: 'A', name: domain_name },
            });

            const record = data.result.find((r: any) => r.name === domain_name);
            if (record) {
                await axios.delete(`${cfRecordURL}/${record.id}`, {
                    headers: { Authorization: `Bearer ${CF_TOKEN}` },
                });
                await this.statusService.updateStatus(instanceName, 'deleted_dns');
                console.log(`🗑️ Deleted DNS record for: ${domain_name}`);
                steps.push({ step: 'deleted_dns', success: true });
            } else {
                console.log(`ℹ️ No DNS record found for: ${domain_name}`);
                steps.push({ step: 'deleted_dns', success: false, message: 'DNS record not found' });
            }
        } catch (err) {
            console.warn(`❌ Failed to delete DNS: ${err.message}`);
            steps.push({ step: 'deleted_dns', success: false, message: err.message });
        }

        try {
            await this.lightsail.send(new DeleteKeyPairCommand({ keyPairName: keyName }));
            await this.statusService.updateStatus(instanceName, 'deleted_ssh_key');
            console.log(`🗝️ Deleted SSH key from Lightsail: ${keyName}`);
            steps.push({ step: 'deleted_ssh_key', success: true });
        } catch (err) {
            console.warn(`⚠️ Failed to delete SSH key: ${err.message}`);
            steps.push({ step: 'deleted_ssh_key', success: false, message: err.message });
        }

        try {
            await this.s3
                .deleteObject({
                    Bucket: this.SSH_KEY_BUCKET,
                    Key: `${instanceName}/${keyName}.pem`,
                })
                .promise();

            console.log(`🧹 Deleted key from S3: s3://${this.SSH_KEY_BUCKET}/${instanceName}/${keyName}.pem`);
            await this.statusService.updateStatus(instanceName, 'deleted_key_s3');
            steps.push({ step: 'deleted_key_s3', success: true });
        } catch (err) {
            console.warn(`⚠️ Failed to delete key from S3: ${err.message}`);
            steps.push({ step: 'deleted_key_s3', success: false, message: err.message });
        }

        return steps;
    }
}
