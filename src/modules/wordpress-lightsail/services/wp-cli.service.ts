import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { StatusService } from './status.service';
import * as fs from 'fs';
import * as path from 'path';
import { execSync } from 'child_process';
import { LightsailClient } from '@aws-sdk/client-lightsail';
import * as AWS from 'aws-sdk';

@Injectable()
export class WpCliService {
    private readonly lightsail: LightsailClient;
    private readonly s3: AWS.S3;
    private readonly S3_BUCKET: string;
    private readonly AWS_REGION: string;

    constructor(
        private readonly configService: ConfigService,
        private readonly statusService: StatusService
    ) {
        this.AWS_REGION = this.configService.get<string>('AWS_REGION', 'ap-southeast-1');
        this.S3_BUCKET = this.configService.get<string>('SSH_KEY_BUCKET', '');
        this.lightsail = new LightsailClient({ region: this.AWS_REGION });
        this.s3 = new AWS.S3();
    }

    /**
     * Tải key từ S3 về thư mục tạm nếu chưa có
     */
    async getKeyPathFromS3(instanceName: string, keyName: string): Promise<string> {
        const tmpPath = `/tmp/${keyName}.pem`;

        if (fs.existsSync(tmpPath)) return tmpPath;

        try {
            const { Body } = await this.s3
                .getObject({
                    Bucket: this.S3_BUCKET,
                    Key: `${instanceName}/${keyName}.pem`,
                })
                .promise();

            fs.writeFileSync(tmpPath, Body as Buffer, { mode: 0o600 });
            return tmpPath;
        } catch (err) {
            throw new Error(`Failed to retrieve SSH key from S3: ${err.message}`);
        }
    }

    /**
     * Chạy lệnh WP CLI qua SSH
     */
    async runWpCliCommand(domain_name: string, command: string): Promise<string> {
        const instanceName = domain_name.replace(/\./g, '-');
        const keyName = `${instanceName}-key`;

        // 🔍 Lấy IP từ status Mongo
        const logs = await this.statusService.getStatus(instanceName);
        const ipEntry = logs?.filter((log) => log.step === 'public_ip_obtained' && (log as any).ip).at(-1);
        const ip = (ipEntry as any)?.ip;

        if (!ip) {
            await this.statusService.updateStatus(instanceName, 'wpcli_ip_missing', false, { command });
            throw new Error('Instance IP not found in status log');
        }

        // 🔐 Tải key từ S3 nếu cần
        const keyPath = await this.getKeyPathFromS3(instanceName, keyName);

        const sshCommand = `ssh -o StrictHostKeyChecking=no -i ${keyPath} bitnami@${ip} "sudo wp ${command} --allow-root --path=/opt/bitnami/wordpress"`;

        try {
            const output = execSync(sshCommand, { timeout: 30000 }).toString();
            await this.statusService.updateStatus(instanceName, 'wpcli_command_executed', true, {
                command,
                output: output.length > 1000 ? output.slice(0, 1000) + '...' : output,
            });
            return output;
        } catch (err) {
            await this.statusService.updateStatus(instanceName, 'wpcli_command_failed', false, {
                command,
                error: err.message,
            });
            throw err;
        }
    }
}
