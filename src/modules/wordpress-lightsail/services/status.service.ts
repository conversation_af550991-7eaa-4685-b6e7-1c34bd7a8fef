import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DeploymentStatusEntity } from '../../../entities/deployment-status.entity';

@Injectable()
export class StatusService {
    constructor(
        @InjectRepository(DeploymentStatusEntity)
        private deploymentStatusRepository: Repository<DeploymentStatusEntity>
    ) {}

    /**
     * Cập nhật trạng thái deployment cho một domain
     * @param {string} domain
     * @param {string} step
     * @param {boolean} [success=true]
     * @param {object} [extra={}]
     */
    async updateStatus(domain: string, step: string, success: boolean = true, extra: Record<string, any> = {}) {
        const record = this.deploymentStatusRepository.create({
            domain,
            timestamp: new Date().toISOString(),
            step,
            success,
            extra,
        });
        await this.deploymentStatusRepository.save(record);
    }

    /**
     * <PERSON><PERSON>y toàn bộ trạng thái theo domain
     * @param {string} domain
     * @returns {Promise<Array>}
     */
    async getStatus(domain: string): Promise<DeploymentStatusEntity[]> {
        return await this.deploymentStatusRepository.find({
            where: { domain },
            order: { timestamp: 'ASC' },
        });
    }
}
