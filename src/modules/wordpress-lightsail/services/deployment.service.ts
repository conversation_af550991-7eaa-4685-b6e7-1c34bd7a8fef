import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { StatusService } from './status.service';
import * as fs from 'fs';
import * as path from 'path';
import { execSync } from 'child_process';
import * as net from 'net';
import {
    LightsailClient,
    GetInstanceCommand,
    CreateInstancesCommand,
    CreateKeyPairCommand,
    DeleteKeyPairCommand,
} from '@aws-sdk/client-lightsail';
import * as AWS from 'aws-sdk';
import axios from 'axios';

@Injectable()
export class DeploymentService {
    private readonly lightsail: LightsailClient;
    private readonly s3: AWS.S3;
    private readonly AWS_REGION: string;
    private readonly SSH_KEY_BUCKET: string;

    constructor(
        private readonly configService: ConfigService,
        private readonly statusService: StatusService
    ) {
        this.AWS_REGION = this.configService.get<string>('AWS_REGION', 'ap-southeast-1');
        this.SSH_KEY_BUCKET = this.configService.get<string>('SSH_KEY_BUCKET', '');
        this.lightsail = new LightsailClient({ region: this.AWS_REGION });
        this.s3 = new AWS.S3();
    }

    async waitForSSH(ip: string, timeout: number = 180000): Promise<boolean> {
        const start = Date.now();
        while (Date.now() - start < timeout) {
            const isOpen = await new Promise<boolean>((resolve) => {
                const socket = net.createConnection({ host: ip, port: 22 }, () => {
                    socket.destroy();
                    resolve(true);
                });
                socket.on('error', () => resolve(false));
            });
            if (isOpen) return true;
            await new Promise((r) => setTimeout(r, 5000));
        }
        throw new Error('SSH port 22 not open after timeout');
    }

    async updateCloudflareDNS(domain: string, ip: string): Promise<void> {
        const ZONE_ID = this.configService.get<string>('CF_ZONE_ID');
        const CF_TOKEN = this.configService.get<string>('CF_API_TOKEN');
        const cfRecordURL = `https://api.cloudflare.com/client/v4/zones/${ZONE_ID}/dns_records`;

        const listResp = await axios.get(cfRecordURL, {
            headers: { Authorization: `Bearer ${CF_TOKEN}` },
            params: { type: 'A', name: domain },
        });

        const existing = listResp.data.result.find((r: any) => r.name === domain);
        const dnsRecord = { type: 'A', name: domain, content: ip, ttl: 300, proxied: true };

        if (existing) {
            await axios.put(`${cfRecordURL}/${existing.id}`, dnsRecord, {
                headers: { Authorization: `Bearer ${CF_TOKEN}`, 'Content-Type': 'application/json' },
            });
        } else {
            await axios.post(cfRecordURL, dnsRecord, {
                headers: { Authorization: `Bearer ${CF_TOKEN}`, 'Content-Type': 'application/json' },
            });
        }
    }

    async performDeployment(domain_name: string, bundleId: string, plugins: string[] = []): Promise<void> {
        const instanceName = domain_name.replace(/\./g, '-');
        const keyName = `${instanceName}-key`;

        console.log(`📌 [${domain_name}] → Creating key pair`);
        await this.statusService.updateStatus(instanceName, 'creating_key_pair', true);

        try {
            await this.lightsail.send(new DeleteKeyPairCommand({ keyPairName: keyName }));
        } catch {}

        const keyResult = await this.lightsail.send(new CreateKeyPairCommand({ keyPairName: keyName }));

        await this.s3
            .putObject({
                Bucket: this.SSH_KEY_BUCKET,
                Key: `${instanceName}/${keyName}.pem`,
                Body: keyResult.privateKeyBase64,
                ACL: 'private',
            })
            .promise();
        console.log(`✅ Uploaded SSH key to S3: s3://${this.SSH_KEY_BUCKET}/${instanceName}/${keyName}.pem`);

        console.log(`📌 [${domain_name}] → Creating Lightsail instance`);
        await this.statusService.updateStatus(instanceName, 'creating_instance', true);

        await this.lightsail.send(
            new CreateInstancesCommand({
                instanceNames: [instanceName],
                availabilityZone: `${this.AWS_REGION}a`,
                blueprintId: 'wordpress',
                bundleId,
                keyPairName: keyName,
            })
        );

        await this.statusService.updateStatus(instanceName, 'instance_created', true);

        let ip: string | null = null;
        for (let i = 0; i < 10; i++) {
            const res = await this.lightsail.send(new GetInstanceCommand({ instanceName }));
            ip = res.instance?.publicIpAddress || null;
            if (ip) break;
            await new Promise((r) => setTimeout(r, 8000));
        }

        if (!ip) throw new Error('Failed to obtain public IP');
        console.log(`📌 [${domain_name}] → Instance has public IP`);
        await this.statusService.updateStatus(instanceName, 'public_ip_obtained', true, { ip });

        await this.updateCloudflareDNS(domain_name, ip);
        console.log(`📌 [${domain_name}] → DNS record updated`);
        await this.statusService.updateStatus(instanceName, 'dns_updated', true);

        await this.waitForSSH(ip);
        console.log(`📌 [${domain_name}] → SSH port is open`);
        await this.statusService.updateStatus(instanceName, 'ssh_open', true);

        const keyPath = `/tmp/${keyName}.pem`;
        const { Body } = await this.s3
            .getObject({
                Bucket: this.SSH_KEY_BUCKET,
                Key: `${instanceName}/${keyName}.pem`,
            })
            .promise();
        fs.writeFileSync(keyPath, Body as Buffer, { mode: 0o600 });

        const sshCommand = `ssh -o StrictHostKeyChecking=no -i ${keyPath} bitnami@${ip}`;

        let wpPass = 'unknown';
        const maxRetries = 8;
        for (let i = 0; i < maxRetries; i++) {
            try {
                const output = execSync(
                    `${sshCommand} "cat /home/<USER>/bitnami_credentials || cat /opt/bitnami/wordpress/bitnami_application_password"`,
                    { timeout: 30000 }
                );
                const content = output.toString();
                if (/credentials are not yet available/i.test(content)) {
                    await new Promise((r) => setTimeout(r, 10000));
                    continue;
                }
                const match = content.match(/'user'\s+and\s+'([^']+)'/i);
                wpPass = match ? match[1].trim() : 'unknown';
                break;
            } catch (err) {
                if (i === maxRetries - 1) throw new Error('Failed to get WP password');
            }
        }

        console.log(`📌 [${domain_name}] → Retrieved WordPress password`);
        await this.statusService.updateStatus(instanceName, 'retrieved_wp_password', true, { password: wpPass });

        if (Array.isArray(plugins) && plugins.length > 0) {
            console.log(`📌 [${domain_name}] → Installing plugins`);
            await this.statusService.updateStatus(instanceName, 'installing_plugins', true, { plugins });

            for (const plugin of plugins) {
                try {
                    const cmd = `sudo wp plugin install ${plugin} --activate --allow-root --path=/opt/bitnami/wordpress`;
                    const output = execSync(`${sshCommand} "${cmd}"`, { timeout: 30000 });
                    console.log(`✅ Plugin ${plugin} installed:\n${output.toString()}`);
                    await this.statusService.updateStatus(instanceName, `plugin_installed_${plugin}`, true);
                } catch (err) {
                    console.warn(`⚠️ Plugin ${plugin} failed: ${err.message}`);
                    await this.statusService.updateStatus(instanceName, `plugin_installed_${plugin}`, false, {
                        error: err.message,
                    });
                }
            }

            await this.statusService.updateStatus(instanceName, 'plugins_installed', true);
        }

        console.log(`📌 [${domain_name}] → Deployment complete`);
        await this.statusService.updateStatus(instanceName, 'deployment_complete', true, {
            domain: domain_name,
            ip,
            wp_user: 'user',
            wp_password: wpPass,
        });
    }
}
