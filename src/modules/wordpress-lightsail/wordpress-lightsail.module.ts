import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { StatusService } from './services/status.service';
import { WpCliService } from './services/wp-cli.service';
import { DeploymentService } from './services/deployment.service';
import { DeleteService } from './services/delete.service';
import { StatusController } from './controllers/status.controller';
import { WpCliController } from './controllers/wp-cli.controller';
import { DeploymentController } from './controllers/deployment.controller';
import { DeleteController } from './controllers/delete.controller';
import { DeploymentStatusEntity } from '../../entities/deployment-status.entity';

@Module({
    imports: [ConfigModule, TypeOrmModule.forFeature([DeploymentStatusEntity])],
    controllers: [<PERSON><PERSON><PERSON>roller, WpCliController, DeploymentController, DeleteController],
    providers: [StatusService, WpCliService, DeploymentService, DeleteService],
    exports: [StatusService, WpCliService, DeploymentService, DeleteService],
})
export class WordPressLightsailModule {}
