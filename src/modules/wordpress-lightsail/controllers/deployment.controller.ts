import { Controller, Post, Body } from '@nestjs/common';
import { DeploymentService } from '../services/deployment.service';

export interface PerformDeploymentDto {
    domain_name: string;
    bundleId: string;
    plugins?: string[];
}

@Controller('wordpress-lightsail/deployment')
export class DeploymentController {
    constructor(private readonly deploymentService: DeploymentService) {}

    /**
     * Perform WordPress deployment on Lightsail
     */
    @Post()
    async performDeployment(@Body() performDeploymentDto: PerformDeploymentDto) {
        const { domain_name, bundleId, plugins = [] } = performDeploymentDto;
        await this.deploymentService.performDeployment(domain_name, bundleId, plugins);
        return { message: 'Deployment started successfully' };
    }
}
