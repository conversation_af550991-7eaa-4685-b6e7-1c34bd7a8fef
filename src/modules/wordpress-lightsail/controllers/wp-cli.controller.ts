import { Controller, Post, Param, Body } from '@nestjs/common';
import { WpCliService } from '../services/wp-cli.service';

export interface RunWpCliCommandDto {
    command: string;
}

@Controller('wordpress-lightsail/wpcli')
export class WpCliController {
    constructor(private readonly wpCliService: WpCliService) {}

    /**
     * Chạy lệnh WP CLI qua SSH
     */
    @Post(':domain')
    async runWpCliCommand(@Param('domain') domain: string, @Body() runWpCliCommandDto: RunWpCliCommandDto) {
        const { command } = runWpCliCommandDto;
        const output = await this.wpCliService.runWpCliCommand(domain, command);
        return { output };
    }
}
