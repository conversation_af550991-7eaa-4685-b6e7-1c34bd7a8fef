import { Controller, Get, Post, Param, Body } from '@nestjs/common';
import { StatusService } from '../services/status.service';

export interface UpdateStatusDto {
    step: string;
    success?: boolean;
    extra?: Record<string, any>;
}

@Controller('wordpress-lightsail/status')
export class StatusController {
    constructor(private readonly statusService: StatusService) {}

    @Post(':domain')
    async updateStatus(@Param('domain') domain: string, @Body() updateStatusDto: UpdateStatusDto) {
        const { step, success = true, extra = {} } = updateStatusDto;
        await this.statusService.updateStatus(domain, step, success, extra);
        return { message: 'Status updated successfully' };
    }

    /**
     * <PERSON><PERSON><PERSON> tất cả trạng thái của một domain
     */
    @Get(':domain')
    async getStatus(@Param('domain') domain: string) {
        return this.statusService.getStatus(domain);
    }
}
