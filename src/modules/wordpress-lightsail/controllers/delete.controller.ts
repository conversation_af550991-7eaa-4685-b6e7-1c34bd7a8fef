import { Controller, Delete, Param } from '@nestjs/common';
import { DeleteService } from '../services/delete.service';

@Controller('wordpress-lightsail/delete')
export class DeleteController {
    constructor(private readonly deleteService: DeleteService) {}

    @Delete(':domain')
    async deleteResources(@Param('domain') domain: string) {
        const steps = await this.deleteService.deleteResources(domain);
        return { steps };
    }
}
