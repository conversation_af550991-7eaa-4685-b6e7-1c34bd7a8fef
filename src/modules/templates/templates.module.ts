import { Module } from '@nestjs/common';
import { TemplatesService } from './services/templates.service';
import { TemplatesResolver } from './resolvers/templates.resolver';
import { TemplateMetaResolver } from './resolvers/template-meta.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TemplateEntity } from '../../entities/template.entity';
import { TemplateMetaEntity } from '../../entities/template-meta.entity';
import { CronTemplateEntity } from '../../entities/cron-template.entity';
import { FileEntity } from '../../entities/file.entity';
import { UserEntity } from '../../entities/user.entity';

@Module({
    imports: [
        TypeOrmModule.forFeature([TemplateEntity, TemplateMetaEntity, CronTemplateEntity, FileEntity, UserEntity]),
    ],
    providers: [TemplatesService, TemplatesResolver, TemplateMetaResolver],
    exports: [TemplatesService],
})
export class TemplatesModule {}
