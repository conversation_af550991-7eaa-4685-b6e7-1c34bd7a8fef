import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { TemplateEntity } from '../../../entities/template.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { ITemplateSaveInput } from '../dtos/template-save-input.dto';
import { IndustryEntity } from '../../../entities/industry.entity';
import { FileEntity } from '../../../entities/file.entity';
import { UserEntity } from '../../../entities/user.entity';
import { TemplateListInput } from '../dtos/template-list.dto';
import { ReviewsieuxeAxios } from '../../../axios/reviewsieuxe.axios';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { downloadFileToTemp } from '../../../commons/helpers/file.helper';
import { ItemStatus } from '../../../commons/enums.common';
const AdmZip = require('adm-zip');

@Injectable()
export class TemplatesService extends BaseService<TemplateEntity> {
    constructor(
        private readonly rsAxios: ReviewsieuxeAxios,
        @InjectRepository(TemplateEntity)
        public readonly repo: Repository<TemplateEntity>,
        @InjectRepository(FileEntity)
        private readonly fileRepo: Repository<FileEntity>,
        @InjectRepository(UserEntity)
        private readonly userRepo: Repository<UserEntity>
    ) {
        super(repo);
    }

    async getSearch(options: TemplateListInput): Promise<TemplateEntity[]> {
        const query = this.buildQuery(options);

        if (!!options.popularOrder) {
            query.addSelect('COUNT(w.template_id)', 'usage_count');

            query.leftJoin('websites', 'w', 'entity.id = w.template_id').groupBy('entity.id, entity.name');

            if (options.popularOrder.toUpperCase() === 'DESC') {
                query.addOrderBy('usage_count', 'DESC');
            } else {
                query.addOrderBy('usage_count', 'ASC');
            }
        }

        return query.getMany();
    }

    private async validateTemplateFiles(body: ITemplateSaveInput): Promise<void> {
        const fileIds = [
            body.image_id,
            body.csv_file_id,
            body.template_file_id,
            body.code_file_id,
            body.sql_file_id,
        ].filter((id) => id !== undefined && id !== null);

        if (fileIds.length === 0) return;

        const files = await this.fileRepo.find({
            where: { id: In(fileIds) },
        });

        const fileMap = new Map(files.map((file) => [file.id, file]));

        if (body.csv_file_id) {
            const csvFile = fileMap.get(body.csv_file_id);
            if (!csvFile) throw new NotFoundException('CSV file not found');
            if (csvFile.mime_type !== 'text/csv' || !csvFile.file_name.endsWith('.csv')) {
                throw new BadRequestException('Invalid CSV file type');
            }
        }

        if (body.template_file_id) {
            const templateFile = fileMap.get(body.template_file_id);
            if (!templateFile) throw new NotFoundException('Template file not found');
            if (!(templateFile.mime_type === 'application/zip' && templateFile.file_name.endsWith('.zip'))) {
                throw new BadRequestException('Template file must be a ZIP file or JSON file');
            }
        }

        if (body.code_file_id) {
            const sourceFile = fileMap.get(body.code_file_id);
            if (!sourceFile) throw new NotFoundException('Source file not found');
            if (sourceFile.mime_type !== 'application/zip' || !sourceFile.file_name.endsWith('.zip')) {
                throw new BadRequestException('Source file must be a ZIP file');
            }
        }

        if (body.sql_file_id) {
            const dbFile = fileMap.get(body.sql_file_id);
            if (!dbFile) throw new NotFoundException('Database file not found');
            const validDbTypes = ['application/zip', 'application/sql', 'text/plain', 'application/octet-stream'];
            if (!validDbTypes.includes(dbFile.mime_type) || !dbFile.file_name.endsWith('.sql')) {
                throw new BadRequestException('Database file must be either a ZIP or SQL file');
            }
        }
    }

    async saveTemplate(body: ITemplateSaveInput, id?: number): Promise<TemplateEntity> {
        //validate file
        if (body.is_kit && body.template_file_id) {
            const file = await this.repo.manager.findOne(FileEntity, {
                where: { id: body.template_file_id },
            });
            if (!file) throw new BadRequestException('File template not found');
            if (!file?.file_url) {
                throw new BadRequestException('File template not found');
            }

            await this.extractAndValidateTemplateZip(file.file_url);
        }
        //end validate file

        let template: TemplateEntity = new TemplateEntity();
        if (id) {
            template = (await this.findOne(id)) as TemplateEntity;
            if (!template) throw new NotFoundException();
        }
        await this.validateTemplateFiles(body);

        Object.assign(template, body);
        if (ItemStatus.ACTIVE === body.status_id) {
            template.approved_date = new Date();
        }
        if (body.industry_ids)
            template.industries = await this.repo.manager.find(IndustryEntity, {
                where: { id: In(body.industry_ids) },
            });

        return this.repo.manager.transaction(async (run) => {
            return run.save(template);
        });
    }

    /**
     * Like a template for the authenticated user
     */
    async likeTemplate(templateId: number, userId: number): Promise<TemplateEntity> {
        const template = await this.repo.findOne({
            where: { id: templateId },
            relations: ['user'],
        });

        if (!template) {
            throw new NotFoundException('Template not found');
        }

        const user = await this.userRepo.findOne({
            where: { id: userId },
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        // Initialize the user array if it doesn't exist
        if (!template.user) {
            template.user = [];
        }

        // Check if the user has already liked the template
        const userExists = template.user.some((u) => u.id === userId);
        if (!userExists) {
            template.user.push(user);
            await this.repo.save(template);
        }

        return template;
    }

    /**
     * Unlike a template for the authenticated user
     */
    async unlikeTemplate(templateId: number, userId: number): Promise<TemplateEntity> {
        const template = await this.repo.findOne({
            where: { id: templateId },
            relations: ['user'],
        });

        if (!template) {
            throw new NotFoundException('Template not found');
        }

        // Check if the user has liked the template
        if (template.user && template.user.length > 0) {
            template.user = template.user.filter((u) => u.id !== userId);
            await this.repo.save(template);
        }

        return template;
    }

    async destroy(id: number, deletedBy: number): Promise<void> {
        const template = await this.findOne(id);
        if (!template) {
            throw new NotFoundException();
        }
        if (template.status_id === ItemStatus.ACTIVE) throw new ForbiddenException();
        if (!template.is_kit && template.info?.domain?.length) {
            await this.repo.manager.transaction(async (run) => {
                await run.update(TemplateEntity, { id }, { deleted_by: deletedBy, deleted_at: new Date() });
                await this.rsAxios.delete(template.info.domain);
            });
        } else await this.repo.update({ id }, { deleted_by: deletedBy, deleted_at: new Date() });
    }

    /**
     * Extract and validate template zip file
     * giải nén, check trong đó có file này ko [manifest.json, site-settings.json]
     * @param filePath
     * @private
     */
    private async extractAndValidateTemplateZip(filePath: string): Promise<void> {
        const zipPath = await downloadFileToTemp(filePath);
        const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'template-'));

        try {
            const zip = new AdmZip(zipPath);
            zip.extractAllTo(tempDir, true);

            const requiredFiles = ['manifest.json']; //'site-settings.json'
            const extractedFiles = fs.readdirSync(tempDir);

            for (const file of requiredFiles) {
                if (!extractedFiles.includes(file)) {
                    throw new BadRequestException('Invalid file: Missing required files in template');
                }
            }
        } finally {
            fs.rmSync(tempDir, { recursive: true, force: true });
            fs.unlinkSync(zipPath);
        }
    }
}
