import { <PERSON><PERSON>, Resolve<PERSON>ield, Resolver } from '@nestjs/graphql';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { UserEntity } from '../../../entities/user.entity';
import { TemplateMetaEntity } from '../../../entities/template-meta.entity';
import { TemplateEntity } from '../../../entities/template.entity';

@Resolver(TemplateMetaEntity)
export class TemplateMetaResolver {
    constructor(private readonly dataLoaderService: DataLoaderService) {}

    @ResolveField(() => TemplateEntity)
    async template(@Parent() templateMeta: TemplateMetaEntity): Promise<TemplateEntity | null> {
        if (!templateMeta.template_id) return null;
        return this.dataLoaderService.relationBatchOne(TemplateEntity).load(templateMeta.template_id);
    }

    @ResolveField(() => UserEntity)
    async user(@Parent() templateMeta: TemplateMetaEntity): Promise<UserEntity | null> {
        if (!templateMeta.user_id) return null;
        return this.dataLoaderService.relationBatchOne(UserEntity).load(templateMeta.user_id);
    }
}
