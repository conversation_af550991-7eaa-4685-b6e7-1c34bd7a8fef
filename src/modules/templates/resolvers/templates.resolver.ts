import { <PERSON><PERSON><PERSON>, <PERSON>t, <PERSON><PERSON>, Query, <PERSON>sol<PERSON><PERSON><PERSON>, Resolver } from '@nestjs/graphql';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { UserEntity, UserRoles } from '../../../entities/user.entity';
import { IndustryEntity } from '../../../entities/industry.entity';
import { AuthMutation } from '../../../commons/decorators/graphql.decorators';
import { Roles } from '../../../commons/decorators/roles.decorator';
import { AuthUser } from '../../auth/auth.decorator';
import { TemplateEntity } from '../../../entities/template.entity';
import { TemplateMetaEntity } from '../../../entities/template-meta.entity';
import { CronTemplateEntity } from '../../../entities/cron-template.entity';
import { TemplatesService } from '../services/templates.service';
import { <PERSON>Entity } from '../../../entities/file.entity';
import { TemplateSaveInputDto } from '../dtos/template-save-input.dto';
import { TemplateChangeStatusInputDto } from '../dtos/template-change-status-input.dto';
import { NotFoundException } from '@nestjs/common';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { TemplateModel } from '../models/template.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { IAdditionalQuery } from '../../../commons/bases/base.service';

@Resolver(TemplateEntity)
export class TemplatesResolver {
    constructor(
        private readonly templatesService: TemplatesService,
        private readonly dataLoaderService: DataLoaderService
    ) {}

    @ResolveField(() => [IndustryEntity], { nullable: true })
    async industries(@Parent() template: TemplateEntity): Promise<IndustryEntity[]> {
        return this.dataLoaderService.relationBatchManyMany(IndustryEntity, 'templates').load(template.id);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async designer(@Parent() template: TemplateEntity): Promise<UserEntity | null> {
        if (!template.designer_id) return null;
        return this.dataLoaderService.relationBatchOne(UserEntity).load(template.designer_id);
    }

    @ResolveField(() => [UserEntity], { nullable: true })
    async user(@Parent() template: TemplateEntity): Promise<UserEntity[]> {
        return this.dataLoaderService.relationBatchManyMany(UserEntity, 'userTemplates').load(template.id);
    }

    @ResolveField(() => [FileEntity], { nullable: true })
    async image(@Parent() template: TemplateEntity): Promise<FileEntity | null> {
        if (!template.image_id) return null;
        return this.dataLoaderService.relationBatchOne(FileEntity).load(template.image_id);
    }

    @ResolveField(() => [FileEntity], { nullable: true })
    async csvFile(@Parent() template: TemplateEntity): Promise<FileEntity | null> {
        if (!template.csv_file_id) return null;
        return this.dataLoaderService.relationBatchOne(FileEntity).load(template.csv_file_id);
    }

    @ResolveField(() => [FileEntity], { nullable: true })
    async codeFile(@Parent() template: TemplateEntity): Promise<FileEntity | null> {
        if (!template.code_file_id) return null;
        return this.dataLoaderService.relationBatchOne(FileEntity).load(template.code_file_id);
    }

    @ResolveField(() => [FileEntity], { nullable: true })
    async templateFile(@Parent() template: TemplateEntity): Promise<FileEntity | null> {
        if (!template.template_file_id) return null;
        return this.dataLoaderService.relationBatchOne(FileEntity).load(template.template_file_id);
    }

    @ResolveField(() => [FileEntity], { nullable: true })
    async sqlFile(@Parent() template: TemplateEntity): Promise<FileEntity | null> {
        if (!template.sql_file_id) return null;
        return this.dataLoaderService.relationBatchOne(FileEntity).load(template.sql_file_id);
    }

    @ResolveField(() => [TemplateMetaEntity], { nullable: true })
    async templateMeta(@Parent() template: TemplateEntity): Promise<TemplateMetaEntity[]> {
        return this.dataLoaderService.relationBatchOneMany(TemplateMetaEntity, 'template_id').load(template.id);
    }

    @ResolveField(() => [CronTemplateEntity], { nullable: true })
    async cronTemplates(@Parent() template: TemplateEntity): Promise<CronTemplateEntity[]> {
        return this.dataLoaderService.relationBatchOneMany(CronTemplateEntity, 'template_id').load(template.id);
    }

    @ResolveField(() => [FileEntity], { nullable: true })
    async rejectFile(@Parent() template: TemplateEntity): Promise<FileEntity | null> {
        if (!template.reject_file_id) return null;
        return this.dataLoaderService.relationBatchOne(FileEntity).load(template.reject_file_id);
    }

    @Query(() => TemplateModel, {
        name: 'template_list',
        description: 'sắp xếp templates theo độ phổ biến => usage_count:ASC/DESC',
    })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<TemplateEntity>> {
        let additionalOptions: IAdditionalQuery = {};
        if (body.sort?.includes('usage_count')) {
            const sort = body.sort.split(':');
            delete body.sort;
            additionalOptions = {
                select: ['(select count(*) from websites where template_id = entity.id) as usage_count'],
                order: { sort: sort[0], order: sort[1] as 'ASC' | 'DESC' },
            };
            body = { ...body, ...additionalOptions };
        }
        return this.templatesService.search(body, additionalOptions);
    }

    @Query(() => TemplateEntity, { name: 'template_view' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<TemplateEntity> {
        return this.templatesService.findOne(id).then((sp) => {
            if (!sp) throw new NotFoundException();
            return sp;
        });
    }

    @AuthMutation(() => TemplateEntity, { name: 'template_create' })
    @Roles(UserRoles.ADMIN)
    async store(@Args('body') body: TemplateSaveInputDto, @AuthUser() auth: UserEntity): Promise<TemplateEntity> {
        return this.templatesService.saveTemplate({ ...body, created_by: auth.id });
    }

    @AuthMutation(() => TemplateEntity, { name: 'template_update' })
    @Roles(UserRoles.ADMIN)
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: TemplateSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<TemplateEntity> {
        return this.templatesService.saveTemplate({ ...body, updated_by: auth.id }, id);
    }

    @AuthMutation(() => Boolean, { name: 'template_delete' })
    @Roles(UserRoles.ADMIN)
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<Boolean> {
        await this.templatesService.destroy(id, auth.id);
        return true;
    }

    @AuthMutation(() => TemplateEntity, { name: 'template_change_status' })
    @Roles(UserRoles.ADMIN)
    async changeStatus(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: TemplateChangeStatusInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<TemplateEntity> {
        return this.templatesService.updateOne(id, { updated_by: auth.id, ...body });
    }

    @AuthMutation(() => TemplateEntity, { name: 'template_like' })
    async likeTemplate(
        @Args('id', { type: () => Int }) id: number,
        @AuthUser() auth: UserEntity
    ): Promise<TemplateEntity> {
        return this.templatesService.likeTemplate(id, auth.id);
    }

    @AuthMutation(() => TemplateEntity, { name: 'template_unlike' })
    async unlikeTemplate(
        @Args('id', { type: () => Int }) id: number,
        @AuthUser() auth: UserEntity
    ): Promise<TemplateEntity> {
        return this.templatesService.unlikeTemplate(id, auth.id);
    }
}
