import { Field, GraphQLISODateTime, InputType, Int } from '@nestjs/graphql';
import { IsArray, IsBoolean, IsDate, IsEnum, IsNotEmpty, IsObject, IsOptional, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { ItemStatus } from '../../../commons/enums.common';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { IndustryEntity } from '../../../entities/industry.entity';
import { UserEntity } from '../../../entities/user.entity';
import { FileEntity } from '../../../entities/file.entity';
import { GraphQLJSON } from 'graphql-type-json';

@InputType()
export class TemplateSaveInputDto {
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @Field()
    name: string;

    @IsOptional()
    @IsString()
    @Field(() => String, { nullable: true })
    desc?: string;

    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(ItemStatus)
    @Field(() => Int)
    status_id: ItemStatus;

    @IsOptional()
    @IdExists(UserEntity)
    @Field(() => Int, { nullable: true })
    designer_id?: number;

    @IsOptional()
    @IdExists(FileEntity)
    @Field(() => Int, { nullable: true })
    image_id?: number;

    @IsOptional()
    @IdExists(FileEntity)
    @Field(() => Int, { nullable: true })
    csv_file_id?: number;

    @IsOptional()
    @IdExists(FileEntity)
    @Field(() => Int, { nullable: true })
    code_file_id?: number;

    @IsOptional()
    @IsObject()
    @Field(() => GraphQLJSON, { nullable: true })
    info?: any;

    @IsOptional()
    @IsArray()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED, each: true })
    @IdExists(IndustryEntity, { each: true })
    @Field(() => [Int], { nullable: true })
    industry_ids?: number[];

    @IsOptional()
    @IsBoolean()
    @Field(() => Boolean, { nullable: true })
    is_multiple?: boolean;

    @IsOptional()
    @IsDate()
    @Field(() => GraphQLISODateTime, { nullable: true })
    approved_date?: Date;

    @IsOptional()
    @IdExists(FileEntity)
    @Field(() => Int, { nullable: true })
    template_file_id?: number;

    @IsOptional()
    @IdExists(FileEntity)
    @Field(() => Int, { nullable: true })
    sql_file_id?: number;

    @IsBoolean()
    @Field(() => Boolean)
    is_kit: boolean;

    @IsOptional()
    @IdExists(FileEntity)
    @Field(() => Int, { nullable: true })
    reject_file_id?: number;

    @IsOptional()
    @IsString()
    @Field(() => String, { nullable: true })
    reject_reason?: string;
}

export interface ITemplateSaveInput {
    name: string;
    desc?: string;
    status_id: ItemStatus;
    designer_id?: number;
    image_id?: number;
    csv_file_id?: number;
    code_file_id?: number;
    template_file_id?: number;
    sql_file_id?: number;
    info?: any;
    industry_ids?: number[];
    created_by?: number;
    updated_by?: number;
    is_multiple?: boolean;
    approved_date?: Date;
    is_kit: boolean;
    reject_file_id?: number;
    reject_reason?: string;
}
