import { HttpStatus, Module } from '@nestjs/common';
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { join } from 'path';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppDataSource } from './configs/data-source.conf';
import { Request, Response } from 'express';
import { UsersModule } from './modules/users/users.module';
import { AuthModule } from './modules/auth/auth.module';
import appConf from './configs/app.conf';
import { GroupActionsModule } from './modules/group-actions/group-actions.module';
import { FilesModule } from './modules/files/files.module';
import * as depthLimit from 'graphql-depth-limit';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { AssignIdToBodyInterceptor } from './commons/interceptors/assign-id-to-body.interceptor';
import { DataLoaderModule } from './data-loaders/data-loaders.module';
import { IndustriesModule } from './modules/industries/industries.module';
import { TemplatesModule } from './modules/templates/templates.module';
import { AxiosModule } from './axios/axios.module';
import { NamecheapModule } from './modules/namecheap/namecheap.module';
import { SubscriptionsModule } from './modules/subscriptions/subscriptions.module';
import { PaymentModule } from './modules/payments/payment.module';
import { EmailModule } from './modules/e-mails/email.module';
import { WebsiteStylesModule } from './modules/website-styles/website-styles.module';
import { WebsitesModule } from './modules/websites/websites.module';
import { TransactionsModule } from './modules/transactions/transactions.module';
import { sendErrorToSlack } from './commons/helpers/slack.helper';
import { ScheduleModule } from '@nestjs/schedule';
import { TaskSchedulingModule } from './task-scheduling/task-scheduling.module';
import { LoggerModule } from './loggers/logger.module';
import { TemplateComponentsModule } from './modules/template-components/template-components.module';
import { WebhooksModule } from './modules/webhooks/webhooks.module';
import { MasterTagsModule } from './modules/master-tags/master-tags.module';
import { WordpressApisModule } from './modules/wordpress-apis/wordpress-apis.module';
import { CloudflareModule } from './modules/cloudflare/cloudflare.module';
import { CronTemplatesModule } from './modules/cron-templates/cron-templates.module';
import { ConfigsModule } from './modules/configs/configs.module';
import { SmtpModule } from './modules/stmp/stmp.module';
import { WordPressLightsailModule } from './modules/wordpress-lightsail/wordpress-lightsail.module';
import { BackupWebsitesModule } from './modules/backup-websites/backup-websites.module';
import { TicketsModule } from './modules/tickets/tickets.module';

@Module({
    imports: [
        ConfigModule.forRoot({
            isGlobal: true,
        }),
        TypeOrmModule.forRoot(AppDataSource.options),
        GraphQLModule.forRoot<ApolloDriverConfig>({
            driver: ApolloDriver,
            autoSchemaFile: join(process.cwd(), 'schema.gql'),
            playground: appConf.NODE_ENV !== 'production', // 🔥 Chỉ bật Playground trong môi trường Dev
            introspection: appConf.NODE_ENV !== 'production', // Chặn introspection khi production
            context: ({ req, res }: { req: Request; res: Response }) => ({ req, res }),
            formatError: (error) => {
                if (appConf.NODE_ENV !== 'production') console.log('🔥 Errors:', error); // Debug lỗi
                const statusCode = error.extensions?.code ?? HttpStatus.INTERNAL_SERVER_ERROR;
                // Gửi log đến Slack khi là INTERNAL_SERVER_ERROR
                if (statusCode === HttpStatus.INTERNAL_SERVER_ERROR) {
                    sendErrorToSlack(error, null, 'GraphQL');
                }
                return {
                    message: error.message,
                    code: statusCode,
                    errors: error.extensions?.errors,
                };
            },
            validationRules: [depthLimit(10)], // Chỉ cho phép vào sâu 10 cấp độ
            plugins: [
                {
                    async requestDidStart() {
                        return {
                            async willSendResponse({ response, errors }) {
                                if (errors?.length) {
                                    const httpStatus =
                                        // @ts-ignore
                                        (errors[0]?.extensions?.http?.status ?? errors[0]?.extensions?.code) || 500;
                                    if (response.http) {
                                        response.http.status = +httpStatus;
                                    }
                                }
                            },
                        };
                    },
                },
            ],
        }),
        ScheduleModule.forRoot(),
        UsersModule,
        AuthModule,
        GroupActionsModule,
        FilesModule,
        DataLoaderModule,
        IndustriesModule,
        TemplatesModule,
        AxiosModule,
        NamecheapModule,
        SubscriptionsModule,
        PaymentModule,
        EmailModule,
        WebsiteStylesModule,
        WebsitesModule,
        TransactionsModule,
        TaskSchedulingModule,
        LoggerModule,
        TemplateComponentsModule,
        WebhooksModule,
        MasterTagsModule,
        WordpressApisModule,
        CloudflareModule,
        CronTemplatesModule,
        ConfigsModule,
        SmtpModule,
        WordPressLightsailModule,
        BackupWebsitesModule,
        TicketsModule,
        //--------Module services--------//
    ],
    providers: [
        {
            provide: APP_INTERCEPTOR,
            useClass: AssignIdToBodyInterceptor,
        },
    ],
})
export class AppModule {}
